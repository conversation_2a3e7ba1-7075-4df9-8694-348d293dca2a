<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_XlnVcBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_XlnVcRTCEe2ht7vZ3PkGHQ" bindingContexts="_XlnVehTCEe2ht7vZ3PkGHQ">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.ide.FileStoreEditorInputFactory&quot; id=&quot;org.eclipse.wst.xml.ui.internal.tabletree.XMLMultiPageEditorPart&quot; name=&quot;.project&quot; tooltip=&quot;D:\eclipse\cust_Windchill_src\.project&quot;>&#xD;&#xA;&lt;persistable uri=&quot;file:/D:/eclipse/cust_Windchill_src/.project&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;/mruList>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_XlnVcRTCEe2ht7vZ3PkGHQ" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_X0HEcxTCEe2ht7vZ3PkGHQ" label="%trimmedwindow.label.eclipseSDK" x="64" y="64" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1659706058841"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_X0HEcxTCEe2ht7vZ3PkGHQ" selectedElement="_X0HEdBTCEe2ht7vZ3PkGHQ" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_X0HEdBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_X8vcARTCEe2ht7vZ3PkGHQ">
        <children xsi:type="advanced:Perspective" xmi:id="_X8vcARTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.JavaPerspective" selectedElement="_X8vcAhTCEe2ht7vZ3PkGHQ" label="Java" iconURI="platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/eview16/jperspective.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,persp.hideToolbarSC:opengroovyprojectwizard,"/>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.codehaus.groovy.eclipse.quickfix.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.TypeHierarchy</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.SourceView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.JavadocView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.JavaProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewPackageCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewInterfaceCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewEnumCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewRecordCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewAnnotationCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSnippetFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewJavaWorkingSetWizard</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.file</tags>
          <tags>persp.newWizSC:org.eclipse.ui.editors.wizards.UntitledTextFileWizard</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaBrowsingPerspective</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.showIn:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.showIn:org.eclipse.team.ui.GenericHistoryView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.debug.ui.JDTDebugActionSet</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.actionSet:org.eclipse.eclemma.ui.CoverageActionSet</tags>
          <tags>persp.showIn:org.eclipse.eclemma.ui.CoverageView</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.junit.wizards.NewTestCaseCreationWizard</tags>
          <tags>persp.actionSet:org.eclipse.jdt.junit.JUnitActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ant.ui.views.AntView</tags>
          <tags>persp.actionSet:org.codehaus.groovy.eclipse.conversion</tags>
          <tags>persp.actionSet:org.codehaus.groovy.eclipse.ui.groovyElementCreation</tags>
          <tags>persp.newWizSC:org.codehaus.groovy.eclipse.ui.groovyProjectWizard</tags>
          <tags>persp.newWizSC:org.codehaus.groovy.eclipse.ui.groovyClassWizard</tags>
          <tags>persp.newWizSC:org.codehaus.groovy.eclipse.ui.groovyJUnitWizard</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_X8vcAhTCEe2ht7vZ3PkGHQ" selectedElement="_X8vcAxTCEe2ht7vZ3PkGHQ" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_X8vcAxTCEe2ht7vZ3PkGHQ" containerData="2500" selectedElement="_X8vcBBTCEe2ht7vZ3PkGHQ">
              <children xsi:type="basic:PartStack" xmi:id="_X8vcBBTCEe2ht7vZ3PkGHQ" elementId="left" containerData="6000" selectedElement="_X8vcBRTCEe2ht7vZ3PkGHQ">
                <tags>org.eclipse.e4.primaryNavigationStack</tags>
                <tags>active</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_X8vcBRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.PackageExplorer" ref="_X8chEBTCEe2ht7vZ3PkGHQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_X8vcBhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_X8chERTCEe2ht7vZ3PkGHQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_X8vcBxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" toBeRendered="false" ref="_X8chEhTCEe2ht7vZ3PkGHQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_X8vcCBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.junit.ResultView" toBeRendered="false" ref="_X8mSExTCEe2ht7vZ3PkGHQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_X8vcCRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesViewMStack" toBeRendered="false" containerData="4000">
                <children xsi:type="advanced:Placeholder" xmi:id="_X8vcChTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesView" toBeRendered="false" ref="_X8mSERTCEe2ht7vZ3PkGHQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Git</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_X8vcCxTCEe2ht7vZ3PkGHQ" containerData="7500" selectedElement="_X8vcDBTCEe2ht7vZ3PkGHQ">
              <children xsi:type="basic:PartSashContainer" xmi:id="_X8vcDBTCEe2ht7vZ3PkGHQ" containerData="7500" selectedElement="_X8vcDhTCEe2ht7vZ3PkGHQ" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_X8vcDRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_X8JmIBTCEe2ht7vZ3PkGHQ"/>
                <children xsi:type="basic:PartStack" xmi:id="_X8vcDhTCEe2ht7vZ3PkGHQ" elementId="right" containerData="2500" selectedElement="_X8vcDxTCEe2ht7vZ3PkGHQ">
                  <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_X8vcDxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.ContentOutline" ref="_X8chGhTCEe2ht7vZ3PkGHQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_X8vcEBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.texteditor.TemplatesView" toBeRendered="false" ref="_X8chGxTCEe2ht7vZ3PkGHQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_X8vcERTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_X8mSEBTCEe2ht7vZ3PkGHQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_X8vcEhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ant.ui.views.AntView" toBeRendered="false" ref="_X8vcABTCEe2ht7vZ3PkGHQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Ant</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_X8vcExTCEe2ht7vZ3PkGHQ" elementId="bottom" containerData="2500" selectedElement="_X8vcFBTCEe2ht7vZ3PkGHQ">
                <tags>org.eclipse.e4.secondaryDataStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_X8vcFBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.ProblemView" ref="_X8chExTCEe2ht7vZ3PkGHQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_X8vcFRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.JavadocView" ref="_X8chFBTCEe2ht7vZ3PkGHQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_X8vcFhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.SourceView" ref="_X8chFRTCEe2ht7vZ3PkGHQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_X8vcFxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.search.ui.views.SearchView" toBeRendered="false" ref="_X8chFhTCEe2ht7vZ3PkGHQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_X8vcGBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.console.ConsoleView" toBeRendered="false" ref="_X8chFxTCEe2ht7vZ3PkGHQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_X8vcGRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_X8chGBTCEe2ht7vZ3PkGHQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_X8vcGhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_X8chGRTCEe2ht7vZ3PkGHQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_X8vcGxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_X8mSEhTCEe2ht7vZ3PkGHQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_X0HEdRTCEe2ht7vZ3PkGHQ" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_X0HEdhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_X0HEcBTCEe2ht7vZ3PkGHQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_X0HEdxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_X0HEcRTCEe2ht7vZ3PkGHQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_X0HEeBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_X0HEchTCEe2ht7vZ3PkGHQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_X0HEcBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_X0HEcRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view>&#xD;&#xA;&lt;presentation currentPage=&quot;qroot&quot; restore=&quot;true&quot;/>&#xD;&#xA;&lt;standbyPart/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_YN3BMBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YN3BMRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_X0HEchTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_X8JmIBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.editorss" selectedElement="_X8JmIRTCEe2ht7vZ3PkGHQ">
      <children xsi:type="basic:PartStack" xmi:id="_X8JmIRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.e4.primaryDataStack">
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>EditorStack</tags>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_X8chEBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.PackageExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view group_libraries=&quot;1&quot; layout=&quot;2&quot; linkWithEditor=&quot;0&quot; rootMode=&quot;1&quot; workingSetName=&quot;Aggregate for window 1659706058841&quot;>&#xD;&#xA;&lt;customFilters userDefinedPatternsEnabled=&quot;false&quot;>&#xD;&#xA;&lt;xmlDefinedFilters>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.StaticsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.buildfolder&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer_patternFilterId_.*&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonSharedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.SyntheticMembersFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ContainedLibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.HideInnerClassFilesFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyInnerPackageFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.m2e.MavenModuleFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.subProject&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ClosedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.DeprecatedMembersFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.EmptyLibraryContainerFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.PackageDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ImportDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaElementFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.CuAndClassFileFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyPackageFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonPublicFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LocalTypesFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.FieldsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;/xmlDefinedFilters>&#xD;&#xA;&lt;/customFilters>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <tags>active</tags>
      <tags>activeOnClose</tags>
      <menus xmi:id="_YAGSABTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.PackageExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YAGSARTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.PackageExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_X8chERTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_X8chEhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_X8chExTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot; partName=&quot;Problems&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;120&quot; org.eclipse.ui.ide.markerType=&quot;120&quot; org.eclipse.ui.ide.pathField=&quot;160&quot; org.eclipse.ui.ide.resourceField=&quot;120&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;400&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_YHBZYBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YHBZYRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_X8chFBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.JavadocView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_X8chFRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.SourceView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_X8chFhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_X8chFxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_X8chGBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_X8chGRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_X8chGhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_YGBT0BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YGBT0RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_X8chGxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.texteditor.TemplatesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_X8mSEBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_X8mSERTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Git</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_X8mSEhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Terminal</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_X8mSExTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.junit.ResultView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_X8vcABTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ant.ui.views.AntView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Ant</tags>
    </sharedElements>
    <trimBars xmi:id="_XlnVchTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.platform">
      <children xsi:type="menu:ToolBar" xmi:id="_X15NIBTCEe2ht7vZ3PkGHQ" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_X15NIRTCEe2ht7vZ3PkGHQ" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_X15NIhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_l2EG0BTDEe2CPJjvlHg64Q" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_XmTTiBTCEe2ht7vZ3PkGHQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_X15NIxTCEe2ht7vZ3PkGHQ" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_X15NJBTCEe2ht7vZ3PkGHQ" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_X15NJRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.workbench.edit" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_X15NJhTCEe2ht7vZ3PkGHQ" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_X15NJxTCEe2ht7vZ3PkGHQ" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_X-OpwBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_X9oz4BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_X-Ff0BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_X15NKBTCEe2ht7vZ3PkGHQ" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_X15NKRTCEe2ht7vZ3PkGHQ" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_X15NKhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_l2EG3RTDEe2CPJjvlHg64Q" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" enabled="false" type="Check" command="_XmTTOhTCEe2ht7vZ3PkGHQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_X15NKxTCEe2ht7vZ3PkGHQ" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_X15NLBTCEe2ht7vZ3PkGHQ" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_X15NLRTCEe2ht7vZ3PkGHQ" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_X15NLhTCEe2ht7vZ3PkGHQ" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_X15NLxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_X3Op4BTCEe2ht7vZ3PkGHQ" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_X3Op4RTCEe2ht7vZ3PkGHQ" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_XlnVcxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.platform" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_XlnVdBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_XlnVdRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_XlnVdhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_XlnVdxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_YP5BgBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_XlnVeBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.platform" side="Right"/>
  </children>
  <bindingTables xmi:id="_XlnVeRTCEe2ht7vZ3PkGHQ" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_XlnVehTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvW5BTCEe2ht7vZ3PkGHQ" keySequence="CTRL+1" command="_XmI67hTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXBRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+I" command="_XmI6zxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXDhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+L" command="_XmTTwRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXHBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SPACE" command="_XmTTWhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXKhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+D" command="_XmTT5xTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXUxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+V" command="_XmI6RhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXcRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+SPACE" command="_XmI7CxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXchTCEe2ht7vZ3PkGHQ" keySequence="CTRL+A" command="_XmTSIRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXfxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+C" command="_XmTSfRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H6xTCEe2ht7vZ3PkGHQ" keySequence="CTRL+X" command="_XmI7LxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H7BTCEe2ht7vZ3PkGHQ" keySequence="CTRL+Y" command="_XmTSSxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H7xTCEe2ht7vZ3PkGHQ" keySequence="CTRL+Z" command="_XmI7JxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IAhTCEe2ht7vZ3PkGHQ" keySequence="ALT+PAGE_UP" command="_XmTSWRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IAxTCEe2ht7vZ3PkGHQ" keySequence="ALT+PAGE_DOWN" command="_XmTTDRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IBhTCEe2ht7vZ3PkGHQ" keySequence="SHIFT+INSERT" command="_XmI6RhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IDRTCEe2ht7vZ3PkGHQ" keySequence="ALT+F11" command="_XmI6khTCEe2ht7vZ3PkGHQ">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_Xm5IRRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+F10" command="_XmI6bBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IThTCEe2ht7vZ3PkGHQ" keySequence="CTRL+INSERT" command="_XmTSfRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IXBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+PAGE_UP" command="_XmTTnxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IXRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+PAGE_DOWN" command="_XmI69RTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IXxTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+F3" command="_XmTTkhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IZBTCEe2ht7vZ3PkGHQ" keySequence="SHIFT+DEL" command="_XmI7LxTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_Xmll0BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.textEditorScope" bindingContext="_Xmcb4hTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvW0BTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+CR" command="_XmTTkRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvW0RTCEe2ht7vZ3PkGHQ" keySequence="CTRL+BS" command="_XmI6FRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvW4RTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+Q" command="_XmI61xTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXCBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+J" command="_XmI6yBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXDBTCEe2ht7vZ3PkGHQ" keySequence="CTRL++" command="_XmTS6RTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXERTCEe2ht7vZ3PkGHQ" keySequence="CTRL+-" command="_XmTSAxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXJhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+C" command="_XmTS4BTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXMRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+F" command="_XmI7FhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXPhTCEe2ht7vZ3PkGHQ" keySequence="ALT+CTRL+J" command="_XmI65hTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXRxTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+A" command="_XmTSnRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXWhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+J" command="_XmI6dBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXXBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+L" command="_XmTTcBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXZxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+O" command="_XmTSgxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXbRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+/" command="_XmTSERTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXhBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+D" command="_XmI6ghTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H4RTCEe2ht7vZ3PkGHQ" keySequence="CTRL+=" command="_XmTS6RTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H5xTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+Y" command="_XmI6CxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H8hTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+DEL" command="_XmTTYRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H8xTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+X" command="_XmTShRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H9BTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+Y" command="_XmTSABTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H-BTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+\" command="_XmI7ORTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H-xTCEe2ht7vZ3PkGHQ" keySequence="CTRL+DEL" command="_XmI7IRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H_RTCEe2ht7vZ3PkGHQ" keySequence="ALT+ARROW_UP" command="_XmTUGxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H_hTCEe2ht7vZ3PkGHQ" keySequence="ALT+ARROW_DOWN" command="_XmTTFhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IBBTCEe2ht7vZ3PkGHQ" keySequence="SHIFT+END" command="_XmTSChTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IDhTCEe2ht7vZ3PkGHQ" keySequence="SHIFT+HOME" command="_XmTR9BTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IFBTCEe2ht7vZ3PkGHQ" keySequence="END" command="_XmTTrRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IFRTCEe2ht7vZ3PkGHQ" keySequence="INSERT" command="_XmTSuxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IGxTCEe2ht7vZ3PkGHQ" keySequence="F2" command="_XmI69xTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IKBTCEe2ht7vZ3PkGHQ" keySequence="HOME" command="_XmTTzxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IKxTCEe2ht7vZ3PkGHQ" keySequence="ALT+CTRL+ARROW_UP" command="_XmTT-RTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5ILBTCEe2ht7vZ3PkGHQ" keySequence="ALT+CTRL+ARROW_DOWN" command="_XmTSKhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IMBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+INSERT" command="_XmI6txTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IOhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_XmTSDRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IPBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_XmI6vhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IRhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+F10" command="_XmTTjRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5ITBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+END" command="_XmTTGBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IVxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+ARROW_UP" command="_XmI6ohTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IWBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+ARROW_DOWN" command="_XmTUKhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IWhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+ARROW_LEFT" command="_XmTSdxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IWxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+ARROW_RIGHT" command="_XmI61BTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IXhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+HOME" command="_XmI6RBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IYBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+NUMPAD_MULTIPLY" command="_XmTTJhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IYRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+NUMPAD_ADD" command="_XmTT7BTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IYhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+NUMPAD_SUBTRACT" command="_XmTTjxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IYxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+NUMPAD_DIVIDE" command="_XmI6pRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IaxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_XmTTLxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IcBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_XmTSvRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IjBTCEe2ht7vZ3PkGHQ" keySequence="ALT+/" command="_XmTT1BTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IkBTCEe2ht7vZ3PkGHQ" keySequence="SHIFT+CR" command="_XmTTzhTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_XmvW0hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.contexts.window" bindingContext="_XlnVexTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvW0xTCEe2ht7vZ3PkGHQ" keySequence="ALT+CTRL+SHIFT+T" command="_XmI6axTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvW1BTCEe2ht7vZ3PkGHQ" keySequence="ALT+CTRL+SHIFT+L" command="_XmTSsRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvW3RTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+Q O" command="_XmTS_BTCEe2ht7vZ3PkGHQ">
      <parameters xmi:id="_XmvW3hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_XmvW3xTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+Q P" command="_XmTS_BTCEe2ht7vZ3PkGHQ">
      <parameters xmi:id="_XmvW4BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.PackageExplorer"/>
    </bindings>
    <bindings xmi:id="_XmvW5RTCEe2ht7vZ3PkGHQ" keySequence="ALT+CTRL+B" command="_XmTTBBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvW5hTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+R" command="_XmTULxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvW5xTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+Q Q" command="_XmTS_BTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvW6BTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+S" command="_XmTS2xTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvW6RTCEe2ht7vZ3PkGHQ" keySequence="CTRL+3" command="_XmI69hTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvW6hTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+T" command="_XmI7LhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvW6xTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+T" command="_XmI6qBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvW7RTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+Q S" command="_XmTS_BTCEe2ht7vZ3PkGHQ">
      <parameters xmi:id="_XmvW7hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_XmvW8RTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+Q T" command="_XmTS_BTCEe2ht7vZ3PkGHQ">
      <parameters xmi:id="_XmvW8hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.TypeHierarchy"/>
    </bindings>
    <bindings xmi:id="_XmvW8xTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+U" command="_XmI6tRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvW-BTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+V" command="_XmTTsRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvW-xTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+Q V" command="_XmTS_BTCEe2ht7vZ3PkGHQ">
      <parameters xmi:id="_XmvW_BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_XmvXARTCEe2ht7vZ3PkGHQ" keySequence="ALT+CTRL+G" command="_XmTS8RTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXAhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+W" command="_XmI7LRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXAxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+H" command="_XmTScRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXBhTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+Q H" command="_XmTS_BTCEe2ht7vZ3PkGHQ">
      <parameters xmi:id="_XmvXBxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_XmvXCRTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+Q J" command="_XmTS_BTCEe2ht7vZ3PkGHQ">
      <parameters xmi:id="_XmvXChTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.JavadocView"/>
    </bindings>
    <bindings xmi:id="_XmvXCxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+K" command="_XmI6oBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXDRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+," command="_XmI6TBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXDxTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+Q L" command="_XmTS_BTCEe2ht7vZ3PkGHQ">
      <parameters xmi:id="_XmvXEBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_XmvXEhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+-" command="_XmTTxhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXFhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+N" command="_XmTSkBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXFxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+." command="_XmTT_RTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXGxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+O" command="_XmTT5BTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXHRTCEe2ht7vZ3PkGHQ" keySequence="ALT+CTRL+P" command="_XmI6lRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXIBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+B" command="_XmI6oRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXIhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+#" command="_XmI6bRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXLBTCEe2ht7vZ3PkGHQ" keySequence="ALT+CTRL+T" command="_XmTSeBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXLRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+E" command="_XmI6sBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXNBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+G" command="_XmTUChTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXOBTCEe2ht7vZ3PkGHQ" keySequence="ALT+CTRL+H" command="_XmI6ZBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXOhTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+Q X" command="_XmTS_BTCEe2ht7vZ3PkGHQ">
      <parameters xmi:id="_XmvXOxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_XmvXPBTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+Q Y" command="_XmTS_BTCEe2ht7vZ3PkGHQ">
      <parameters xmi:id="_XmvXPRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_XmvXPxTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+Q Z" command="_XmTS_BTCEe2ht7vZ3PkGHQ">
      <parameters xmi:id="_XmvXQBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_XmvXRRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+P" command="_XmTTiBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXRhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+Q" command="_XmTTlxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXTRTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+C" command="_XmTTYBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXThTCEe2ht7vZ3PkGHQ" keySequence="CTRL+S" command="_XmTSBBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXURTCEe2ht7vZ3PkGHQ" keySequence="CTRL+U" command="_XmTSRRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXUhTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+F" command="_XmTTnBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXVRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+W" command="_XmTSVBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXVhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+H" command="_XmTTWRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXWxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+K" command="_XmTTCBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXYBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+M" command="_XmTTVRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXYxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+N" command="_XmTUDxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXcBTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+P" command="_XmTS9RTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXdBTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+R" command="_XmTSTxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXdRTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+R" command="_XmI6TxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXeBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+B" command="_XmI6URTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXeRTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+Q B" command="_XmTS_BTCEe2ht7vZ3PkGHQ">
      <parameters xmi:id="_XmvXehTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_XmvXgBTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+S" command="_XmTSZxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXgRTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+T" command="_XmTSoBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXghTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+Q C" command="_XmTS_BTCEe2ht7vZ3PkGHQ">
      <parameters xmi:id="_XmvXgxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_XmvXhRTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+Q D" command="_XmTS_BTCEe2ht7vZ3PkGHQ">
      <parameters xmi:id="_XmvXhhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.SourceView"/>
    </bindings>
    <bindings xmi:id="_Xm5H0BTCEe2ht7vZ3PkGHQ" keySequence="CTRL+E" command="_XmI7IBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H0xTCEe2ht7vZ3PkGHQ" keySequence="CTRL+F" command="_XmI6jRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H1BTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+V" command="_XmTSDBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H2BTCEe2ht7vZ3PkGHQ" keySequence="CTRL+G" command="_XmI6FhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H2hTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+W" command="_XmTT-BTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H2xTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+I" command="_XmI6bhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H3RTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+J" command="_XmI7HRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H3xTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+L" command="_XmI66RTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H4BTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+M" command="_XmTT7RTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H4hTCEe2ht7vZ3PkGHQ" keySequence="CTRL+=" command="_XmI7SRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H4xTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+N" command="_XmI7KBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H6BTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+Z" command="_XmTSbBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H8BTCEe2ht7vZ3PkGHQ" keySequence="CTRL+_" command="_XmI7DxTCEe2ht7vZ3PkGHQ">
      <parameters xmi:id="_Xm5H8RTCEe2ht7vZ3PkGHQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_Xm5H9RTCEe2ht7vZ3PkGHQ" keySequence="CTRL+{" command="_XmI7DxTCEe2ht7vZ3PkGHQ">
      <parameters xmi:id="_Xm5H9hTCEe2ht7vZ3PkGHQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_Xm5H_xTCEe2ht7vZ3PkGHQ" keySequence="ALT+ARROW_LEFT" command="_XmI6cBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IARTCEe2ht7vZ3PkGHQ" keySequence="ALT+ARROW_RIGHT" command="_XmI7RRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5ICBTCEe2ht7vZ3PkGHQ" keySequence="SHIFT+F2" command="_XmTSzRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IChTCEe2ht7vZ3PkGHQ" keySequence="SHIFT+F5" command="_XmTSMBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5ICxTCEe2ht7vZ3PkGHQ" keySequence="ALT+J G" command="_XmI7MRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IDBTCEe2ht7vZ3PkGHQ" keySequence="ALT+F7" command="_XmTSpRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IDxTCEe2ht7vZ3PkGHQ" keySequence="ALT+F5" command="_XmTSGxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IEhTCEe2ht7vZ3PkGHQ" keySequence="F11" command="_XmTT4BTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IExTCEe2ht7vZ3PkGHQ" keySequence="F12" command="_XmTTXRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IGhTCEe2ht7vZ3PkGHQ" keySequence="F2" command="_XmI6TxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IHRTCEe2ht7vZ3PkGHQ" keySequence="F3" command="_XmI65xTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IIRTCEe2ht7vZ3PkGHQ" keySequence="F4" command="_XmI6WhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IJhTCEe2ht7vZ3PkGHQ" keySequence="F5" command="_XmI7ThTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IKRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+F7" command="_XmTT4hTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IKhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+F8" command="_XmI7DhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5ILRTCEe2ht7vZ3PkGHQ" keySequence="ALT+CTRL+ARROW_LEFT" command="_XmTTlxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5ILhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+F11" command="_XmTSahTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5ILxTCEe2ht7vZ3PkGHQ" keySequence="ALT+CTRL+ARROW_RIGHT" command="_XmI6pxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IMRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+F4" command="_XmI7LRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IMhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+F6" command="_XmTS5RTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IMxTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+X G" command="_XmTUKRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IOxTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+X J" command="_XmTTEBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IPRTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+X M" command="_XmTSJxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IPhTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+X A" command="_XmI6ShTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IPxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+F7" command="_XmTSfhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IQBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+F8" command="_XmI68BTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5ISRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+F11" command="_XmTTrxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IUhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+F4" command="_XmTSVBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IVBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+F6" command="_XmI6lhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IVRTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+F7" command="_XmTTGhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IVhTCEe2ht7vZ3PkGHQ" keySequence="ALT+CTRL+X G" command="_XmTT3BTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IZhTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+X X" command="_XmTSlRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IZxTCEe2ht7vZ3PkGHQ" keySequence="ALT+G J" command="_XmTS6BTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IahTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_XmTS2hTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IbBTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+X Q" command="_XmI6yxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IbRTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+X R" command="_XmTSShTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IbhTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+X T" command="_XmTSHxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IbxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_XmI7ERTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IchTCEe2ht7vZ3PkGHQ" keySequence="DEL" command="_XmI6mxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IixTCEe2ht7vZ3PkGHQ" keySequence="ALT+-" command="_XmTSjxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IjRTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+E E" command="_XmI6chTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IjhTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+E G" command="_XmI7NRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IjxTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+E J" command="_XmI6RRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IkRTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+E S" command="_XmTSIhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IkhTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+E T" command="_XmI6ahTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IkxTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+E L" command="_XmI6TRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IlBTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+E N" command="_XmTT_xTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IlRTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+E P" command="_XmI6ERTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5ImBTCEe2ht7vZ3PkGHQ" keySequence="ALT+CR" command="_XmTTRxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5ImRTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+E R" command="_XmI62hTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5ImhTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+D G" command="_XmI6fBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5ImxTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+D A" command="_XmTTcxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5InBTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+D R" command="_XmTSTBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5InRTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+D T" command="_XmI6IhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5InhTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+D X" command="_XmTSQhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5InxTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+D J" command="_XmTTMhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IoBTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+D Q" command="_XmTSNhTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_XmvW1RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.javaEditorScope" bindingContext="_Xmcb6hTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvW1hTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+P" command="_XmTTGRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvW_RTCEe2ht7vZ3PkGHQ" keySequence="CTRL+7" command="_XmTSkxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXExTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+M" command="_XmI6-BTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXGBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+/" command="_XmTSkxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXIxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+C" command="_XmTSkxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXMBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+F" command="_XmTTzRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXShTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+B" command="_XmTUHxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXUBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+T" command="_XmTSnBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXVxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+I" command="_XmI7QxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXZhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+O" command="_XmTSIxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXbBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+/" command="_XmTSRhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H0RTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+U" command="_XmTTORTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H1hTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+'" command="_XmTSrBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H5RTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+O" command="_XmI7IhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H9xTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+\" command="_XmI6gxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5INBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+ARROW_UP" command="_XmTSVhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5INxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_XmTSJRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IQRTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+ARROW_UP" command="_XmTSNRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IQxTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_XmI6oxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IRxTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+ARROW_LEFT" command="_XmI7TRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IShTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_XmI6fRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IUBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+F3" command="_XmTT-xTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IeRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+2 F" command="_XmTT6xTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IhRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+2 R" command="_XmTTSxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IhxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+2 T" command="_XmTSkhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IiBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+2 L" command="_XmI6dhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IihTCEe2ht7vZ3PkGHQ" keySequence="CTRL+2 M" command="_XmI7URTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_XmvW1xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.core.runtime.xml" bindingContext="_Xmcb8BTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvW2BTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+P" command="_XmTTXhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXKxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+D" command="_XmTSRBTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_XmvW2RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.javaEditorScope" bindingContext="_Xmcb4xTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvW2hTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+P" command="_XmTS3RTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvW7BTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+T" command="_XmTSPRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXABTCEe2ht7vZ3PkGHQ" keySequence="CTRL+7" command="_XmTStxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXGhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+/" command="_XmTStxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXJxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+C" command="_XmTStxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXMhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+F" command="_XmI6ehTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXWBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+I" command="_XmTSDhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXahTCEe2ht7vZ3PkGHQ" keySequence="CTRL+O" command="_XmI6MxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXbhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+/" command="_XmTSOxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H0hTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+U" command="_XmI7LBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H5hTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+O" command="_XmTR9hTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H-RTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+\" command="_XmI6mRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5INRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+ARROW_UP" command="_XmTUMhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IOBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_XmI6thTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IURTCEe2ht7vZ3PkGHQ" keySequence="CTRL+F3" command="_XmTSghTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IehTCEe2ht7vZ3PkGHQ" keySequence="CTRL+2 F" command="_XmTR-xTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IhhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+2 R" command="_XmI6-hTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IiRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+2 L" command="_XmTT_BTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_XmvW2xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" bindingContext="_Xmcb7BTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvW3BTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+P" command="_XmTSnhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXHxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+A" command="_XmTUKxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXKBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+C" command="_XmTTyxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXMxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+F" command="_XmTUCxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXRBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+>" command="_XmTTdBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXWRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+I" command="_XmTTjBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXaxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+O" command="_XmTSqxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXbxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+/" command="_XmTSjhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H-hTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+\" command="_XmTS8hTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IIBTCEe2ht7vZ3PkGHQ" keySequence="F3" command="_XmTSmxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5INhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+ARROW_UP" command="_XmI6xhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IORTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_XmTSPBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IQhTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+ARROW_UP" command="_XmTTEhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IRBTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_XmTTIxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5ISBTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+ARROW_LEFT" command="_XmI6YhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5ISxTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_XmTSzxTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_XmvW4hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.classFileEditorScope" bindingContext="_Xmcb6BTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvW4xTCEe2ht7vZ3PkGHQ" keySequence="CTRL+1" command="_XmTUCBTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_XmvW7xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.serverViewScope" bindingContext="_XmccARTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvW8BTCEe2ht7vZ3PkGHQ" keySequence="ALT+CTRL+D" command="_XmTTDBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXHhTCEe2ht7vZ3PkGHQ" keySequence="ALT+CTRL+P" command="_XmTTHxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXIRTCEe2ht7vZ3PkGHQ" keySequence="ALT+CTRL+R" command="_XmTT_hTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXKRTCEe2ht7vZ3PkGHQ" keySequence="ALT+CTRL+S" command="_XmI64BTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_XmvW9BTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.editor.groovyEditorScope" bindingContext="_Xmcb6xTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvW9RTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+U" command="_XmI6GBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXXxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+," command="_XmI6UBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXdhTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+R" command="_XmTS-BTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H3BTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+I" command="_XmI6XBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H6RTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+Z" command="_XmTSxBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IZRTCEe2ht7vZ3PkGHQ" keySequence="ALT+G F" command="_XmI65BTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IcRTCEe2ht7vZ3PkGHQ" keySequence="ALT+G M" command="_XmTTSBTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_XmvW9hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.javascriptViewScope" bindingContext="_XmccEBTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvW9xTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+U" command="_XmI60xTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXBBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+H" command="_XmTULRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXNxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+G" command="_XmTSoxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXORTCEe2ht7vZ3PkGHQ" keySequence="ALT+CTRL+H" command="_XmTTThTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H2RTCEe2ht7vZ3PkGHQ" keySequence="CTRL+G" command="_XmTTChTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H3hTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+J" command="_XmTSyhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H6hTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+Z" command="_XmTSGRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5ICRTCEe2ht7vZ3PkGHQ" keySequence="SHIFT+F2" command="_XmTTaRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IHxTCEe2ht7vZ3PkGHQ" keySequence="F3" command="_XmTTHRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IIhTCEe2ht7vZ3PkGHQ" keySequence="F4" command="_XmTSLxTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_XmvW-RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_Xmcb4BTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvW-hTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+V" command="_XmTR-RTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXJRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+C" command="_XmTTPhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H_BTCEe2ht7vZ3PkGHQ" keySequence="ALT+ARROW_UP" command="_XmI6ExTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IABTCEe2ht7vZ3PkGHQ" keySequence="ALT+ARROW_RIGHT" command="_XmTT3xTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IBRTCEe2ht7vZ3PkGHQ" keySequence="SHIFT+INSERT" command="_XmTR-RTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5ITRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+INSERT" command="_XmTTPhTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_XmvW_hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.propertiesEditorScope" bindingContext="_XmccExTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvW_xTCEe2ht7vZ3PkGHQ" keySequence="CTRL+7" command="_XmTSkxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXGRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+/" command="_XmTSkxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXJBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+C" command="_XmTSkxTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_XmvXFBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jst.jsp.ui.structured.text.editor.jsp.scope" bindingContext="_Xmcb-hTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvXFRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+M" command="_XmI7AxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXdxTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+R" command="_XmTT8BTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H1RTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+V" command="_XmTSJBTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_XmvXLhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ant.ui.AntEditorScope" bindingContext="_Xmcb9hTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvXLxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+F" command="_XmTTzRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXcxTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+R" command="_XmI6WRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H5BTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+O" command="_XmI6HxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IBxTCEe2ht7vZ3PkGHQ" keySequence="SHIFT+F2" command="_XmTSrhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IHBTCEe2ht7vZ3PkGHQ" keySequence="F3" command="_XmI6JRTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_XmvXNRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_XmccAhTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvXNhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+G" command="_XmTTrBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IHhTCEe2ht7vZ3PkGHQ" keySequence="F3" command="_XmTTmBTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_XmvXQRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_Xmcb5xTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvXQhTCEe2ht7vZ3PkGHQ" keySequence="ALT+CTRL+M" command="_XmTSHRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXQxTCEe2ht7vZ3PkGHQ" keySequence="ALT+CTRL+N" command="_XmTT7hTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXTxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+T" command="_XmI6-xTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXVBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+W" command="_XmTSwhTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXYhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+N" command="_XmTS7RTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_XmvXSBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.debugging" bindingContext="_XmccBBTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvXSRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+R" command="_XmTSgRTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IEBTCEe2ht7vZ3PkGHQ" keySequence="F7" command="_XmTUABTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IERTCEe2ht7vZ3PkGHQ" keySequence="F8" command="_XmTSthTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IJRTCEe2ht7vZ3PkGHQ" keySequence="F5" command="_XmI6XxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IJxTCEe2ht7vZ3PkGHQ" keySequence="F6" command="_XmTSEBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5ITxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+F2" command="_XmTTZBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IUxTCEe2ht7vZ3PkGHQ" keySequence="CTRL+F5" command="_XmTT2xTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_XmvXSxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" bindingContext="_Xmcb6RTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvXTBTCEe2ht7vZ3PkGHQ" keySequence="ALT+SHIFT+B" command="_XmTUHxTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_XmvXXRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_XmccBRTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvXXhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+," command="_XmTTmxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_XmvXYRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+SHIFT+." command="_XmTTUxTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5H1xTCEe2ht7vZ3PkGHQ" keySequence="CTRL+G" command="_XmTTVBTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_XmvXZBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.DiffViewer" bindingContext="_Xmcb5BTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvXZRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+O" command="_XmTSNBTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_XmvXaBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_XmccDxTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvXaRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+O" command="_XmI6PxTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_XmvXexTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_XmccFBTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvXfBTCEe2ht7vZ3PkGHQ" keySequence="CTRL+C" command="_XmI7ABTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IWRTCEe2ht7vZ3PkGHQ" keySequence="CTRL+ARROW_LEFT" command="_XmI6fhTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_XmvXfRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_XmccCRTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_XmvXfhTCEe2ht7vZ3PkGHQ" keySequence="CTRL+C" command="_XmI6lxTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_Xm5H7RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.console" bindingContext="_Xmcb_RTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_Xm5H7hTCEe2ht7vZ3PkGHQ" keySequence="CTRL+Z" command="_XmTT9hTCEe2ht7vZ3PkGHQ">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_Xm5IFhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_XmccDhTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_Xm5IFxTCEe2ht7vZ3PkGHQ" keySequence="F1" command="_XmI6IxTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_Xm5IGBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" bindingContext="_XmccFRTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_Xm5IGRTCEe2ht7vZ3PkGHQ" keySequence="F2" command="_XmI6nRTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_Xm5IIxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.buildship.ui.contexts.taskview" bindingContext="_XmccERTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_Xm5IJBTCEe2ht7vZ3PkGHQ" keySequence="F5" command="_XmTTyhTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_Xm5IaBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_XmccChTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_Xm5IaRTCEe2ht7vZ3PkGHQ" keySequence="ALT+Y" command="_XmTSjBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IcxTCEe2ht7vZ3PkGHQ" keySequence="ALT+A" command="_XmTSjBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IdBTCEe2ht7vZ3PkGHQ" keySequence="ALT+B" command="_XmTSjBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IdRTCEe2ht7vZ3PkGHQ" keySequence="ALT+C" command="_XmTSjBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IdhTCEe2ht7vZ3PkGHQ" keySequence="ALT+D" command="_XmTSjBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IdxTCEe2ht7vZ3PkGHQ" keySequence="ALT+E" command="_XmTSjBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IeBTCEe2ht7vZ3PkGHQ" keySequence="ALT+F" command="_XmTSjBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IexTCEe2ht7vZ3PkGHQ" keySequence="ALT+G" command="_XmTSjBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IfBTCEe2ht7vZ3PkGHQ" keySequence="ALT+P" command="_XmTSjBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IfRTCEe2ht7vZ3PkGHQ" keySequence="ALT+R" command="_XmTSjBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IfhTCEe2ht7vZ3PkGHQ" keySequence="ALT+S" command="_XmTSjBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IfxTCEe2ht7vZ3PkGHQ" keySequence="ALT+T" command="_XmTSjBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IgBTCEe2ht7vZ3PkGHQ" keySequence="ALT+V" command="_XmTSjBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IgRTCEe2ht7vZ3PkGHQ" keySequence="ALT+W" command="_XmTSjBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IghTCEe2ht7vZ3PkGHQ" keySequence="ALT+H" command="_XmTSjBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IgxTCEe2ht7vZ3PkGHQ" keySequence="ALT+L" command="_XmTSjBTCEe2ht7vZ3PkGHQ"/>
    <bindings xmi:id="_Xm5IhBTCEe2ht7vZ3PkGHQ" keySequence="ALT+N" command="_XmTSjBTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_Xm5IlhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.BreakpointView" bindingContext="_Xmcb4RTCEe2ht7vZ3PkGHQ">
    <bindings xmi:id="_Xm5IlxTCEe2ht7vZ3PkGHQ" keySequence="ALT+CR" command="_XmTScBTCEe2ht7vZ3PkGHQ"/>
  </bindingTables>
  <bindingTables xmi:id="_X8JmIxTCEe2ht7vZ3PkGHQ" bindingContext="_X8JmIhTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8JmJRTCEe2ht7vZ3PkGHQ" bindingContext="_X8JmJBTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8JmJxTCEe2ht7vZ3PkGHQ" bindingContext="_X8JmJhTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8JmKRTCEe2ht7vZ3PkGHQ" bindingContext="_X8JmKBTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8JmKxTCEe2ht7vZ3PkGHQ" bindingContext="_X8JmKhTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8JmLRTCEe2ht7vZ3PkGHQ" bindingContext="_X8JmLBTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8JmLxTCEe2ht7vZ3PkGHQ" bindingContext="_X8JmLhTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8JmMRTCEe2ht7vZ3PkGHQ" bindingContext="_X8JmMBTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8JmMxTCEe2ht7vZ3PkGHQ" bindingContext="_X8JmMhTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8JmNRTCEe2ht7vZ3PkGHQ" bindingContext="_X8JmNBTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8JmNxTCEe2ht7vZ3PkGHQ" bindingContext="_X8JmNhTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8JmORTCEe2ht7vZ3PkGHQ" bindingContext="_X8JmOBTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8JmOxTCEe2ht7vZ3PkGHQ" bindingContext="_X8JmOhTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8JmPRTCEe2ht7vZ3PkGHQ" bindingContext="_X8JmPBTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8JmPxTCEe2ht7vZ3PkGHQ" bindingContext="_X8JmPhTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8JmQRTCEe2ht7vZ3PkGHQ" bindingContext="_X8JmQBTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwERTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwEBTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwExTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwEhTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwFRTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwFBTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwFxTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwFhTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwGRTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwGBTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwGxTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwGhTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwHRTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwHBTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwHxTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwHhTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwIRTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwIBTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwIxTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwIhTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwJRTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwJBTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwJxTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwJhTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwKRTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwKBTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwKxTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwKhTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwLRTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwLBTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwLxTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwLhTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwMRTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwMBTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwMxTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwMhTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwNRTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwNBTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwNxTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwNhTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwORTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwOBTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwOxTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwOhTCEe2ht7vZ3PkGHQ"/>
  <bindingTables xmi:id="_X8SwPRTCEe2ht7vZ3PkGHQ" bindingContext="_X8SwPBTCEe2ht7vZ3PkGHQ"/>
  <rootContext xmi:id="_XlnVehTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_XlnVexTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_XlnVfBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_Xmcb4BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_Xmcb4RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_Xmcb4hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_Xmcb4xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.javaEditorScope" name="Editing JavaScript Source" description="Editing JavaScript Source Context">
          <children xmi:id="_XmccEBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.javascriptViewScope" name="JavaScript View" description="JavaScript View Context"/>
        </children>
        <children xmi:id="_Xmcb5BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.DiffViewer" name="In Diff Viewer"/>
        <children xmi:id="_Xmcb6BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.classFileEditorScope" name="Browsing attached Java Source" description="Browsing attached Java Source Context"/>
        <children xmi:id="_Xmcb6hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.javaEditorScope" name="Editing Java Source" description="Editing Java Source Context">
          <children xmi:id="_Xmcb6xTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.editor.groovyEditorScope" name="Editing Groovy Source" description="Editing Groovy Source Context"/>
        </children>
        <children xmi:id="_Xmcb7BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors">
          <children xmi:id="_Xmcb7RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xml.cleanup" name="XML Source Cleanup" description="XML Source Cleanup"/>
          <children xmi:id="_Xmcb7hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.sse.comments" name="Source Comments in Structured Text Editors" description="Source Comments in Structured Text Editors"/>
          <children xmi:id="_Xmcb7xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jst.jsp.core.jspsource" name="JSP Source" description="JSP Source"/>
          <children xmi:id="_Xmcb8BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.core.runtime.xml" name="Editing XML Source" description="Editing XML Source"/>
          <children xmi:id="_Xmcb8RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xml.occurrences" name="XML Source Occurrences" description="XML Source Occurrences"/>
          <children xmi:id="_Xmcb8hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xml.grammar" name="XML Source Grammar" description="XML Source Grammar"/>
          <children xmi:id="_Xmcb8xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xml.comments" name="XML Source Comments" description="XML Source Comments"/>
          <children xmi:id="_Xmcb9RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xml.expand" name="XML Source Expand/Collapse" description="XML Source Expand/Collapse"/>
          <children xmi:id="_Xmcb9xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.sse.hideFormat" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors"/>
          <children xmi:id="_Xmcb-RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xml.selection" name="XML Source Selection" description="XML Source Selection"/>
          <children xmi:id="_Xmcb-hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jst.jsp.ui.structured.text.editor.jsp.scope" name="Editing JSP Source" description="Editing JSP Source"/>
          <children xmi:id="_Xmcb_xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xml.navigation" name="XML Source Navigation" description="XML Source Navigation"/>
          <children xmi:id="_XmccAxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.json.core.jsonsource" name="%scope.structured.text.editor.json.name" description="%scope.structured.text.editor.json.description"/>
          <children xmi:id="_XmccCxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.css.core.csssource" name="Editing CSS Source" description="Editing CSS Source"/>
          <children xmi:id="_XmccDRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.html.core.htmlsource" name="Editing HTML Source" description="Editing HTML Source"/>
          <children xmi:id="_XmccEhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xml.dependencies" name="XML Source Dependencies" description="XML Source Dependencies"/>
          <children xmi:id="_XmccFhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.html.occurrences" name="HTML Source Occurrences" description="HTML Source Occurrences"/>
        </children>
        <children xmi:id="_Xmcb9hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ant.ui.AntEditorScope" name="Editing Ant Buildfiles" description="Editing Ant Buildfiles Context"/>
        <children xmi:id="_XmccAhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_XmccDBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xsd.ui.text.editor.context" name="Editing XSD context"/>
        <children xmi:id="_XmccDhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_XmccDxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
        </children>
        <children xmi:id="_XmccExTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.propertiesEditorScope" name="Editing Properties Files" description="Editing Properties Files Context"/>
      </children>
      <children xmi:id="_Xmcb5RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_Xmcb5xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_Xmcb_RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_Xmcb_hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_XmccABTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_XmccARTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.serverViewScope" name="In Servers View" description="In Servers View"/>
      <children xmi:id="_XmccBBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_XmccBRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_XmccBhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xsl.debug.ui.context" name="XSLT Debugging" description="Context for debugging XSLT"/>
        <children xmi:id="_XmccBxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.debug.ui.debugging" name="Debugging Java" description="Debugging Java programs"/>
      </children>
      <children xmi:id="_XmccCRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_XmccChTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_XmccERTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.buildship.ui.contexts.taskview" name="In Gradle Tasks View" description="This context is activated when the Gradle Tasks view is in focus"/>
      <children xmi:id="_XmccFBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View">
        <children xmi:id="_XmccFRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" name="In Git Repositories View"/>
      </children>
    </children>
    <children xmi:id="_XlnVfRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
  </rootContext>
  <rootContext xmi:id="_Xmcb5hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_Xmcb6RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" name="Editor Breadcrumb Navigation" description="Editor Breadcrumb Navigation Context"/>
  <rootContext xmi:id="_Xmcb9BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xsd.ui.editor.sourceView" name="XSD Editor Source View" description="XSD Editor Source View"/>
  <rootContext xmi:id="_Xmcb-BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.wsdl.ui.editor.sourceView" name="WSDL Editor Source View" description="WSDL Editor Source View"/>
  <rootContext xmi:id="_Xmcb-xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xsd.ui.editor.designView" name="XSD Editor Design View" description="XSD Editor Design View"/>
  <rootContext xmi:id="_Xmcb_BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_XmccCBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.wsdl.ui.editor.designView" name="WSDL Editor Design View" description="WSDL Editor Design View"/>
  <rootContext xmi:id="_X8JmIhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ant.ui.actionSet.presentation" name="Auto::org.eclipse.ant.ui.actionSet.presentation"/>
  <rootContext xmi:id="_X8JmJBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_X8JmJhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_X8JmKBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_X8JmKhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_X8JmLBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.CoverageActionSet" name="Auto::org.eclipse.eclemma.ui.CoverageActionSet"/>
  <rootContext xmi:id="_X8JmLhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_X8JmMBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_X8JmMhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_X8JmNBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.debug.ui.JDTDebugActionSet" name="Auto::org.eclipse.jdt.debug.ui.JDTDebugActionSet"/>
  <rootContext xmi:id="_X8JmNhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.junit.JUnitActionSet" name="Auto::org.eclipse.jdt.junit.JUnitActionSet"/>
  <rootContext xmi:id="_X8JmOBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.jdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_X8JmOhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.jdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_X8JmPBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.JavaActionSet" name="Auto::org.eclipse.jdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_X8JmPhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.A_OpenActionSet" name="Auto::org.eclipse.jdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_X8JmQBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.CodingActionSet" name="Auto::org.eclipse.jdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_X8SwEBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.SearchActionSet" name="Auto::org.eclipse.jdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_X8SwEhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_X8SwFBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_X8SwFhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_X8SwGBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_X8SwGhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_X8SwHBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_X8SwHhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_X8SwIBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_X8SwIhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_X8SwJBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_X8SwJhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_X8SwKBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_X8SwKhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_X8SwLBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_X8SwLhTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.quickfix.actionSet" name="Auto::org.codehaus.groovy.eclipse.quickfix.actionSet"/>
  <rootContext xmi:id="_X8SwMBTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.ui.groovyElementCreation" name="Auto::org.codehaus.groovy.eclipse.ui.groovyElementCreation"/>
  <rootContext xmi:id="_X8SwMhTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.conversion" name="Auto::org.codehaus.groovy.eclipse.conversion"/>
  <rootContext xmi:id="_X8SwNBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.server.ui.new.actionSet" name="Auto::org.eclipse.wst.server.ui.new.actionSet"/>
  <rootContext xmi:id="_X8SwNhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.server.ui.internal.webbrowser.actionSet" name="Auto::org.eclipse.wst.server.ui.internal.webbrowser.actionSet"/>
  <rootContext xmi:id="_X8SwOBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.web.ui.wizardsActionSet" name="Auto::org.eclipse.wst.web.ui.wizardsActionSet"/>
  <rootContext xmi:id="_X8SwOhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jst.j2ee.J2eeMainActionSet" name="Auto::org.eclipse.jst.j2ee.J2eeMainActionSet"/>
  <rootContext xmi:id="_X8SwPBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.ws.explorer.explorer" name="Auto::org.eclipse.wst.ws.explorer.explorer"/>
  <descriptors xmi:id="_XprWEBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_XzmuIBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ant.ui.views.AntView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" category="Ant" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Ant</tags>
  </descriptors>
  <descriptors xmi:id="_XzmuIRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.buildship.ui.views.taskview" label="Gradle Tasks" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/tasks_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.task.TaskView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_XzmuIhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.buildship.ui.views.executionview" label="Gradle Executions" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/executions_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.execution.ExecutionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_XzmuIxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_XzmuJBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_XzmuJRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_XzmuJhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_XzwfIBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_XzwfIRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_XzwfIhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_XzwfIxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_XzwfJBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.CoverageView" label="Coverage" iconURI="platform:/plugin/org.eclipse.eclemma.ui/icons/full/eview16/coverage.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.eclemma.internal.ui.coverageview.CoverageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.eclemma.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_XzwfJRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_XzwfJhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_XzwfJxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_XzwfKBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_XzwfKRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_XzwfKhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_XzwfKxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" label="Debug Shell" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.debug.ui.display.DisplayView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_XzwfLBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.junit.ResultView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wkBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.PackageExplorer" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wkRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wkhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.ProjectsView" label="Projects" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/projects.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.ProjectsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wkxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.PackagesView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.PackagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wlBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.TypesView" label="Types" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/types.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.TypesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wlRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.MembersView" label="Members" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.MembersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wlhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" allowMultiple="true" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wlxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wmBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wmRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.JavadocView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wmhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.m2e.core.views.MavenRepositoryView" label="Maven Repositories" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/maven_indexes.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenRepositoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wmxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.m2e.core.views.MavenLifecycleMappingsView" label="Maven Lifecycle Mappings" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/main_tab.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenLifecycleMappingsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wnBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.m2e.core.views.MavenBuild" label="Maven Workspace Build" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.build.BuildDebugView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wnRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.oomph.p2.ui.RepositoryExplorer" label="Repository Explorer" iconURI="platform:/plugin/org.eclipse.oomph.p2.ui/icons/obj16/repository.gif" tooltip="" category="Oomph" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.oomph.p2.internal.ui.RepositoryExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.oomph.p2.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Oomph</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wnhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wnxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0woBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0woRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tips.ide.tipPart" label="Tip of the Day" iconURI="platform:/plugin/org.eclipse.tips.ui/icons/lightbulb.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.tips.ide/org.eclipse.tips.ide.internal.TipPart">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wohTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0woxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.OldTerminalsViewHandler"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wpBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wpRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wphTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wpxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wqBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wqRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.ResourceNavigator" label="Navigator (Deprecated)" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.navigator.ResourceNavigator"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wqhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wqxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wrBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wrRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_Xz0wrhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_Xz8FUBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_Xz8FURTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_Xz8FUhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_Xz8FUxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_Xz8FVBTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.astviews.ASTView" label="Groovy AST Viewer" iconURI="platform:/plugin/org.codehaus.groovy.eclipse/groovy16.png" tooltip="Inspect the Abstract Syntax Tree (AST) of a Groovy source" category="Groovy" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.codehaus.groovy.eclipse.astviews.ASTView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.codehaus.groovy.eclipse.astviews"/>
    <tags>View</tags>
    <tags>categoryTag:Groovy</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_Xz8FVRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.gef.ui.palette_view" label="Palette" iconURI="platform:/plugin/org.eclipse.gef/icons/palette_view.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.gef.ui.views.palette.PaletteView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.gef"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_Xz96gBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" label="Snippets" iconURI="platform:/plugin/org.eclipse.wst.common.snippets/icons/snippets_view.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.common.snippets.internal.ui.SnippetsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.common.snippets"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_Xz96gRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.internet.monitor.view" label="TCP/IP Monitor" iconURI="platform:/plugin/org.eclipse.wst.internet.monitor.ui/icons/cview16/monitorView.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.internet.monitor.ui.internal.view.MonitorView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.internet.monitor.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_Xz96ghTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/call_hierarchy.gif" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.jsdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.jsdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_Xz96gxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/source.gif" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.jsdt.internal.ui.infoviews.SourceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.jsdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_Xz96hBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.JavadocView" label="Documentation" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/javadoc.gif" tooltip="JavaScript Documentation" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.jsdt.internal.ui.infoviews.JavadocView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.jsdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_Xz96hRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.server.ui.ServersView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" category="Server" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.server.ui.internal.cnf.ServersView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.server.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Server</tags>
  </descriptors>
  <descriptors xmi:id="_Xz96hhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xml.ui.views.annotations.XMLAnnotationsView" label="Documentation" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/obj16/comment_obj.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xml.ui.internal.views.annotations.XMLAnnotationsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_Xz96hxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xml.ui.contentmodel.view" label="Content Model" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/view16/hierarchy.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xml.ui.internal.views.contentmodel.ContentModelView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_Xz96iBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xml.views.XPathView" label="XPath" iconURI="platform:/plugin/org.eclipse.wst.xml.xpath.ui/icons/full/xpath.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xml.xpath.ui.internal.views.XPathView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xml.xpath.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_Xz96iRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xsl.jaxp.debug.ui.resultview" label="Result" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xsl.jaxp.debug.ui.internal.views.ResultView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xsl.jaxp.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_Xz96ihTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xsl.ui.view.outline" label="Stylesheet Model" iconURI="platform:/plugin/org.eclipse.wst.xsl.ui/icons/full/hierarchy.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xsl.ui.internal.views.stylesheet.StylesheetModelView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xsl.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <trimContributions xmi:id="_2r10UF9tEeO-yojH_y4TJA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_76uUAF9tEeO-yojH_y4TJA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8tJPcF9tEeO-yojH_y4TJA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_9LgmcF9tEeO-yojH_y4TJA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_XmI6CRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6ChTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6CxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6DBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_XmI6BxTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmI6DRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_XmI6DhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.oomph.p2.ui.SearchRequirements" commandName="Search Requirements" category="_XmI5_hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6DxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to field" description="Invokes quick assist and selects 'Convert local variable to field'" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6EBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6ERTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.junitPluginShortcut.coverage" commandName="Coverage JUnit Plug-in Test" description="Coverage JUnit Plug-in Test" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6EhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.FetchGitLabMergeRequest" commandName="Fetch GitLab Merge Request" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6ExTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_XmI5-hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6FBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.oomph.setup.editor.openDiscoveredType" commandName="Open Discovered Type" category="_XmI56BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6FRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6FhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6FxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6GBTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.ui.findOccurrences" commandName="Find occurrences in current file" description="Populates the search view with all occurrecnes of currently selected token in the file" category="_XmI6CBTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6GRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_XmI58hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6GhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6GxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6HBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6HRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_XmI6BxTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmI6HhTCEe2ht7vZ3PkGHQ" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_XmI6HxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ant.ui.toggleMarkOccurrences" commandName="Toggle Ant Mark Occurrences" description="Toggles mark occurrences in Ant editors" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6IBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6IRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6IhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.junit.junitShortcut.debug" commandName="Debug JUnit Test" description="Debug JUnit Test" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6IxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6JBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_XmI6ABTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6JRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ant.ui.open.declaration.command" commandName="Open Declaration" description="Opens the Ant editor on the referenced element" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6JhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6JxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Methods" description="Add delegate methods for a type's fields" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6KBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.gef.ui.palette_view" commandName="Palette" category="_XmI54RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6KRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xml.views.XPathView.prefixes" commandName="&amp;Edit Namespace Prefixes" category="_XmI6BBTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6KhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6KxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6LBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6LRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6LhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6LxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6MBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.commit.UnifiedDiffCommand" commandName="Show Unified Diff" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6MRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_XmI58hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6MhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6MxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6NBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_XmI59BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6NRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.oomph.setup.editor.importProjects" commandName="Import Projects" category="_XmI56BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6NhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6NxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.hideUnusedElements" commandName="Hide Unused Elements" category="_XmI58RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6OBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Fields" description="Choose fields to initialize and constructor from superclass to call " category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6ORTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xml.ui.disable.grammar.constraints" commandName="Turn off Grammar Constraints" description="Turn off grammar Constraints" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6OhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.commands.showElementInTypeHierarchyView" commandName="Show Java Element Type Hierarchy" description="Show a Java element in the Type Hierarchy view" category="_XmI57RTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmI6OxTCEe2ht7vZ3PkGHQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_XmI6PBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6PRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6PhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6PxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6QBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6QRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_XmI5-RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6QhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_XmI5-RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6QxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_XmI59BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6RBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6RRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.localJavaShortcut.coverage" commandName="Coverage Java Application" description="Coverage Java Application" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6RhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6RxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6SBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.oomph.setup.editor.refreshCache" commandName="Refresh Remote Cache" category="_XmI56BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6SRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify var access" description="Invokes quick assist and selects 'Qualify var access'" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6ShTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.run" commandName="Run Java Applet" description="Run Java Applet" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6SxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.exportSession" commandName="Export Session..." category="_XmI58RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6TBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6TRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.scalaShortcut.coverage" commandName="Coverage Scala Application" description="Coverage Scala Application" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6ThTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6TxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_XmI58hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6UBTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.ui.convertToProperty" commandName="Convert to Property" description="Replace Accessor call with Property read/write" category="_XmI57hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6URTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_XmI5-RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6UhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6UxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.dumpExecutionData" commandName="Dump Execution Data" category="_XmI58RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6VBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.debug.ui.commands.ToggleLambdaEntryBreakpoint" commandName="Toggle &amp;Lambda Entry Breakpoint" description="Creates or removes a lambda entry breakpoint  " category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6VRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6VhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.buildship.ui.commands.runtasks" commandName="Run Gradle Tasks" description="Runs all the selected Gradle tasks" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6VxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggleBreadcrumb" commandName="Toggle Java Editor Breadcrumb" description="Toggle the Java editor breadcrumb" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6WBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6WRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ant.ui.renameInFile" commandName="Rename In File" description="Renames all references within the same buildfile" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6WhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6WxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6XBTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.refactoring.command.inlineMethod" commandName="Inline Method..." category="_XmI57hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6XRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_XmI6ARTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6XhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.m2e.actions.LifeCycleGenerateSources.run" commandName="Run Maven Generate Sources" description="Run Maven Generate Sources" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6XxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6YBTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.refactoring.command.extractMethod" commandName="Extract Method..." category="_XmI57hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6YRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6YhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.sse.ui.structure.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6YxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.debug.ui.commands.AddExceptionBreakpoint" commandName="Add Java Exception Breakpoint" description="Add a Java exception breakpoint" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6ZBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6ZRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6ZhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.debug.ui.commands.ToggleTracepoint" commandName="Toggle Tracepoint" description="Creates or removes a tracepoint" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6ZxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_XmI6BxTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmI6aBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_XmI6aRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6ahTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.junitShortcut.coverage" commandName="Coverage JUnit Test" description="Coverage JUnit Test" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6axTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Local Terminal on Selection" category="_XmI58xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6bBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6bRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6bhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or method" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6bxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6cBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6cRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6chTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.workbenchShortcut.coverage" commandName="Coverage Eclipse Application" description="Coverage Eclipse Application" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6cxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_XmI58hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6dBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6dRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6dhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6dxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6eBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.commands.OpenCoverageConfiguration" commandName="Coverage Configurations..." description="Coverage Configurations..." category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6eRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.return.continue.targets" commandName="Search break/continue Target Occurrences in File" description="Search for break/continue target occurrences of a selected target name" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6ehTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6exTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter methods for type's fields" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6fBTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.groovyScriptLaunchShortcut.debug" commandName="Debug Groovy Script" description="Debug Groovy Script" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6fRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6fhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesViewCollapseWorkingTree" commandName="Collapse Working Tree" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6fxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6gBTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.ui.fixCompilerMismatch" commandName="Fix Groovy Compiler Mismatches" description="Opens a dialog to fix all projects that use a compiler version different from what the workspace provides" category="_XmI56hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6gRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6ghTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6gxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6hBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.implementation" commandName="Open Implementation" description="Opens the Implementations of a method or a type in its hierarchy" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6hRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.junit.gotoTest" commandName="Referring Tests" description="Referring Tests" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6hhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6hxTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.dsl.command.refresh" commandName="Refresh DSLDs" description="Refresh DSLD files for the given project" category="_XmI55xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6iBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.openSessionExecutionData" commandName="Open Execution Data" category="_XmI58RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6iRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.commands.showElementInPackageView" commandName="Show Java Element in Package Explorer" description="Select Java element in the Package Explorer view" category="_XmI57RTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmI6ihTCEe2ht7vZ3PkGHQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_XmI6ixTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.oomph.setup.editor.performDropdown" commandName="Perform Dropdown" category="_XmI56BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6jBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.debug.ui.commands.InstanceCount" commandName="Instance Count" description="View the instance count of the selected type loaded in the target VM" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6jRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6jhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6jxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_XmI6BxTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmI6kBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_XmI6kRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6khTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6kxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6lBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_XmI54BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6lRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.m2e.profiles.ui.commands.selectMavenProfileCommand" commandName="Select Maven Profiles" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6lhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6lxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6mBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_XmI5-BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6mRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6mhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6mxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6nBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6nRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6nhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6nxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6oBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6oRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6ohTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6oxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6pBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6pRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6phTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6pxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6qBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.lsp4e.symbolinworkspace" commandName="Go to Symbol in Workspace" category="_XmI58BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6qRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_XmI58hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6qhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.oomph.ui.ToggleOfflineMode" commandName="Toggle Offline Mode" category="_XmI6BhTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6qxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.oomph.setup.editor.openLog" commandName="Open Setup Log" category="_XmI56BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6rBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_XmI5-RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6rRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_XmI58hTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmI6rhTCEe2ht7vZ3PkGHQ" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_XmI6rxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6sBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6sRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6shTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_XmI5-BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6sxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6tBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into Java comments" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6tRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6thTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the JavaScript file" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6txTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6uBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6uRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6uhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6uxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_XmI5-RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6vBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6vRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6vhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6vxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.JavaBrowsingPerspective" commandName="Java Browsing" description="Show the Java Browsing perspective" category="_XmI6ABTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6wBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6wRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6whTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.relaunchSession" commandName="Relaunch Coverage Session" category="_XmI58RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6wxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6xBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6xRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" description="Check out, rename, create, or delete a branch in a git repository" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6xhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xml.ui.previousSibling" commandName="Previous Sibling" description="Go to Previous Sibling" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6xxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6yBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6yRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_XmI6BxTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmI6yhTCEe2ht7vZ3PkGHQ" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_XmI6yxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ant.ui.antShortcut.run" commandName="Run Ant Build" description="Run Ant Build" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6zBTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.debug.ui.groovyShellLaunchShortcut.run" commandName="Run Groovy Shell" description="Run Groovy Shell" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6zRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6zhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6zxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.debug.ui.commands.Inspect" commandName="Inspect" description="Inspect result of evaluating selected text" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI60BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI60RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI60hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI60xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI61BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI61RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.oomph.setup.donate" commandName="Donate" description="Donate to the development of the Eclipse IDE" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI61hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to var" description="Invokes quick assist and selects 'Convert local variable to var'" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI61xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI62BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI62RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI62hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.junitRAPShortcut.coverage" commandName="Coverage RAP JUnit Test" description="Coverage RAP JUnit Test" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI62xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.ReplaceWithTheirs" commandName="Replace Conflicting Files with Their Revision" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI63BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI63RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.debug.ui.commands.Watch" commandName="Watch" description="Create new watch expression" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI63hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI63xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI64BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.server.stop" commandName="Stop" description="Stop the server" category="_XmI57xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI64RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_XmI5-BTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmI64hTCEe2ht7vZ3PkGHQ" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_XmI64xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI65BTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.ui.convertToClosure" commandName="Convert to Closure" description="Convert a method declaration to a closure" category="_XmI57hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI65RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI65hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI65xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI66BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI66RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI66hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.oomph.p2.ui.ExploreRepository" commandName="Explore Repository" category="_XmI5_hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI66xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI67BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="Open Refactoring History " description="Opens the refactoring history" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI67RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI67hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI67xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI68BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI68RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI68hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI68xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_XmI6BxTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmI69BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_XmI69RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI69hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI69xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6-BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6-RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6-hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6-xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6_BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6_RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6_hTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.dsl.command.check_types" commandName="Type check" description="Perform static type checking on selected resources" category="_XmI55xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI6_xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7ABTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7ARTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.oomph.setup.editor.perform.startup" commandName="Perform Setup Tasks (Startup)" category="_XmI56BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7AhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7AxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jst.jsp.ui.add.imports" commandName="Add Im&amp;port" description="Create import declaration for selection" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7BBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_XmI59hTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmI7BRTCEe2ht7vZ3PkGHQ" elementId="url" name="URL"/>
    <parameters xmi:id="_XmI7BhTCEe2ht7vZ3PkGHQ" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_XmI7BxTCEe2ht7vZ3PkGHQ" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_XmI7CBTCEe2ht7vZ3PkGHQ" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_XmI7CRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7ChTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7CxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7DBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_XmI58hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7DRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7DhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7DxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_XmI59hTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmI7EBTCEe2ht7vZ3PkGHQ" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_XmI7ERTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7EhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_XmI59BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7ExTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.lsp4e.togglelinkwitheditor" commandName="Toggle Link with Editor" category="_XmI58BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7FBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7FRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7FhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.lsp4e.format" commandName="Format" category="_XmI58BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7FxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7GBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7GRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7GhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Toggle &quot;Link with Editor and Selection&quot; (Git Repositories View)" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7GxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7HBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.validation.ValidationCommand" commandName="Validate" description="Invoke registered Validators" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7HRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.javadoc.comment" commandName="Add Javadoc Comment" description="Add a Javadoc comment stub to the member element" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7HhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Interactive Rebase" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7HxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7IBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7IRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7IhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in Java editors" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7IxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7JBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected method" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7JRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7JhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7JxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7KBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_XmI58hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7KRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7KhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7KxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_XmI58hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7LBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7LRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_XmI58hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7LhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a Java editor" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7LxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7MBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected method" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7MRTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.ui.convertToGroovy" commandName="Convert to Groovy" description="Converts a file with a java extension to a file with a groovy extension" category="_XmI57hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7MhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7MxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7NBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7NRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.testNgSuiteShortcut.coverage" commandName="Coverage TestNG Suite" description="Coverage TestNG Suite" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7NhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7NxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xml.ui.reload.dependencies" commandName="Reload Dependencies" description="Reload Dependencies" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7OBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7ORTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm4e.languageconfiguration.removeblockcommentcommand" commandName="Remove Block Comment" category="_XmI54hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7OhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.internal.merge.ToggleCurrentChangesCommand" commandName="Ignore Changes from Ancestor to Current Version" description="Toggle ignoring changes only between the ancestor and the current version in a three-way merge comparison" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7OxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7PBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.junit.junitShortcut.rerunFailedFirst" commandName="Rerun JUnit Test - Failures First" description="Rerun JUnit Test - Failures First" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7PRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_XmI58hTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmI7PhTCEe2ht7vZ3PkGHQ" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_XmI7PxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xsd.ui.refactor.makeTypeGlobal" commandName="Make &amp;Anonymous Type Global" description="Promotes anonymous type to global level and replaces its references" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7QBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7QRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7QhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.JavaPerspective" commandName="Java" description="Show the Java perspective" category="_XmI6ABTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7QxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.indent" commandName="Correct Indentation" description="Corrects the indentation of the selected lines" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7RBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7RRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7RhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7RxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_XmI5-RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7SBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.with.resources" commandName="Surround with try-with-resources Block" description="Surround the selected text with a try-with-resources block" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7SRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.gef.zoom_in" commandName="Zoom In" description="Zoom In" category="_XmI6AxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7ShTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7SxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_XmI58hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7TBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7TRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7ThTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_XmI58hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7TxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.ReplaceWithOurs" commandName="Replace Conflicting Files with Our Revision" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7UBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmI7URTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.extractMethodInplace.assist" commandName="Quick Assist - Extract method" description="Invokes quick assist and selects 'Extract to method'" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTR8BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.oomph.p2.ui.SearchRepositories" commandName="Search Repositories" category="_XmI5_hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTR8RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTR8hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTR8xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTR9BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTR9RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTR9hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in JavaScript editors" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTR9xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTR-BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTR-RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_XmI5-hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTR-hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTR-xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to var" description="Invokes quick assist and selects 'Assign to var'" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTR_BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_XmI57RTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTR_RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_XmTR_hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTR_xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSABTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSARTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSAhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.m2e.discovery.ui" commandName="m2e Marketplace" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSAxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSBBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_XmI58hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSBRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.assignAllParamsToNewFields.assist" commandName="Quick Assist - Assign all parameters to new fields" description="Invokes quick assist and selects 'Assign all parameters to new fields'" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSBhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSBxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSCBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_XmI54BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSCRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.buildship.ui.commands.rundefaulttasks" commandName="Run Gradle Default Tasks" description="Runs the default tasks of the selected Gradle project" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSChTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSCxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.removeActiveSession" commandName="Remove Active Session" category="_XmI58RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSDBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSDRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSDhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.indent" commandName="Indent Line" description="Indents the current line" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSDxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSEBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSERTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm4e.languageconfiguration.addblockcommentcommand" commandName="Add Block Comment" category="_XmI54hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSEhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_XmI59BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSExTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_XmI58hTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTSFBTCEe2ht7vZ3PkGHQ" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_XmTSFRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSFhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSFxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSGBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSGRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSGhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.codemining" commandName="Toggle Code Mining" description="Toggle Code Mining Annotations" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSGxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.m2e.core.ui.command.updateProject" commandName="Update Maven Project" description="Update Maven project configuration and dependencies" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSHBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSHRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSHhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSHxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.junit.junitShortcut.run" commandName="Run JUnit Test" description="Run JUnit Test" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSIBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSIRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSIhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.swtBotJunitShortcut.coverage" commandName="Coverage SWTBot Test" description="Coverage SWTBot Test" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSIxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSJBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jst.jsp.ui.refactor.move" commandName="Move" description="Move a Java Element to another package" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSJRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the compilation unit" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSJhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_XmI5-RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSJxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.m2e.core.pomFileAction.run" commandName="Run Maven Build" description="Run Maven Build" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSKBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.linkWithSelection" commandName="Link with Current Selection" category="_XmI58RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSKRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.m2e.actions.LifeCycleInstall.run" commandName="Run Maven Install" description="Run Maven Install" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSKhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSKxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSLBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.oomph.setup.editor.perform" commandName="Perform Setup Tasks" category="_XmI56BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSLRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.raw.paste" commandName="Raw Paste" description="Paste and ignore smart insert setting" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSLhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_XmI5-BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSLxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSMBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSMRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSMhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSMxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSNBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.commit.DiffEditorQuickOutlineCommand" commandName="Quick Outline" description="Show the quick outline for a unified diff" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSNRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSNhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ant.ui.antShortcut.debug" commandName="Debug Ant Build" description="Debug Ant Build" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSNxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSOBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSORTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_XmI59hTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTSOhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_XmTSOxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSPBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xml.ui.nextSibling" commandName="Next Sibling" description="Go to Next Sibling" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSPRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a JavaScript editor" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSPhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_XmI5-BTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTSPxTCEe2ht7vZ3PkGHQ" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_XmTSQBTCEe2ht7vZ3PkGHQ" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_XmTSQRTCEe2ht7vZ3PkGHQ" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_XmTSQhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xsl.debug.ui.launchshortcut.debug" commandName="Debug XSLT Transformation" description="Create a configuration to debug an XSLT transformation" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSQxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSRBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.m2e.core.ui.command.addDependency" commandName="Add Maven Dependency" description="Add Maven dependency" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSRRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.debug.ui.commands.Execute" commandName="Execute" description="Evaluate selected text" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSRhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSRxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.navigate.gotopackage" commandName="Go to Folder" description="Go to Folder" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSSBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSSRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_XmI58hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSShTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.server.launchShortcut.run" commandName="Run on Server" description="Run the current selection on a server" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSSxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSTBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.server.launchShortcut.debug" commandName="Debug on Server" description="Debug the current selection on a server" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSTRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.m2e.editor.RenameArtifactAction" commandName="Rename Maven Artifact..." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSThTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSTxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSUBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm.terminal.view.ui.command.newview" commandName="New Terminal View" category="_XmI58xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSURTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSUhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSUxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.lsp4e.showkindinoutline" commandName="Show Kind in Outline" category="_XmI58BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSVBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_XmI58hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSVRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSVhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the compilation unit" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSVxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_XmI6BxTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTSWBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_XmTSWRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSWhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() methods for the type" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSWxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_XmI57RTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTSXBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_XmTSXRTCEe2ht7vZ3PkGHQ" elementId="sed.tabletree.collapseAll" commandName="Collapse All" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSXhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSXxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_XmI5_RTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTSYBTCEe2ht7vZ3PkGHQ" elementId="title" name="Title"/>
    <parameters xmi:id="_XmTSYRTCEe2ht7vZ3PkGHQ" elementId="message" name="Message"/>
    <parameters xmi:id="_XmTSYhTCEe2ht7vZ3PkGHQ" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_XmTSYxTCEe2ht7vZ3PkGHQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_XmTSZBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSZRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSZhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSZxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSaBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSaRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSahTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.commands.CoverageLast" commandName="Coverage" description="Coverage" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSaxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_XmI59BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSbBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSbRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSbhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSbxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository..." description="Adds an existing Git repository to the Git Repositories view" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTScBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.debug.ui.breakpoint.properties" commandName="Java Breakpoint Properties" description="View and edit the properties for a given Java breakpoint" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTScRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSchTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTScxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.buildship.ui.commands.refreshproject" commandName="Refresh Gradle Project" description="Synchronizes the Gradle builds of the selected projects with the workspace" category="_XmI55RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSdBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSdRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xml.views.XPathView.processor.xpathprocessor" commandName="XPath Processor" category="_XmI6BBTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTSdhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="State" optional="false"/>
  </commands>
  <commands xmi:id="_XmTSdxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSeBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_XmI58xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSeRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_XmI59hTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTSehTCEe2ht7vZ3PkGHQ" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_XmTSexTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.m2e.sourcelookup.ui.openSourceLookupInfoDialog" commandName="Source Lookup Info" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSfBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSfRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSfhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSfxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSgBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSgRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSghTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSgxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.lsp4e.symbolinfile" commandName="Go to Symbol in File" category="_XmI58BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTShBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTShRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTShhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTShxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSiBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSiRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSihTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.resetOnDump" commandName="Reset on Dump" category="_XmI58RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSixTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSjBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_XmI5-hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSjRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSjhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.sse.ui.add.block.comment" commandName="Add Block Comment" description="Add Block Comment" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSjxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSkBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.debug.ui.commands.AllInstances" commandName="All Instances" description="View all instances of the selected type loaded in the target VM" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSkRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" description="Opens selected commit(s) in Commit Viewer(s)" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSkhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.assignInTryWithResources.assist" commandName="Quick Assist - Assign to variable in new try-with-resources block" description="Invokes quick assist and selects 'Assign to variable in new try-with-resources block'" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSkxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSlBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.m2e.actions.LifeCycleTest.run" commandName="Run Maven Test" description="Run Maven Test" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSlRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xsl.debug.ui.launchshortcut.run" commandName="Run XSLT Transformation" description="Create a configuration to debug an XSLT transformation" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSlhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSlxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to field" description="Invokes quick assist and selects 'Assign parameter to field'" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSmBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSmRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.move.inner.to.top.level" commandName="Move Type to New File" description="Move Type to New File" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSmhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSmxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.sse.ui.open.file.from.source" commandName="Open Selection" description="Open an editor on the selected link" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSnBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSnRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSnhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.sse.ui.goto.matching.bracket" commandName="Matching Character" description="Go to Matching Character" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSnxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.FetchGiteaPullRequest" commandName="Fetch Gitea Pull Request" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSoBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSoRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSohTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.override.methods" commandName="Override/Implement Methods" description="Override or implement methods from super types" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSoxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSpBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSpRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSphTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSpxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSqBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository..." description="Clones a Git repository and adds the clone to the Git Repositories view" category="_XmI5_xTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTSqRTCEe2ht7vZ3PkGHQ" elementId="repositoryUri" name="Repository URI"/>
  </commands>
  <commands xmi:id="_XmTSqhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSqxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.sse.ui.quick_outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSrBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.textblock" commandName="Add Text Block" description="Adds Text Block" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSrRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSrhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ant.ui.openExternalDoc" commandName="Open External Documentation" description="Open the External documentation for the current task in the Ant editor" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSrxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Git Repository..." description="Creates a new Git repository and adds it to the Git Repositories view" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSsBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSsRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_XmI59RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSshTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSsxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTStBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_XmI5-BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTStRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSthTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTStxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSuBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_XmI58hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSuRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSuhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSuxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSvBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSvRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSvhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSvxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSwBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_XmI6BxTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTSwRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_XmTSwhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSwxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSxBTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.quickfix.surround.with.quickMenu" commandName="Groovy code templates" category="_XmI6CBTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSxRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSxhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSxxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSyBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSyRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSyhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.javadoc.comment" commandName="Add JSDoc Comment" description="Add a JSDoc comment stub to the member element" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSyxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSzBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSzRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.external.javadoc" commandName="Open Attached Javadoc" description="Open the attached Javadoc of the selected element in a browser" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSzhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTSzxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.sse.ui.structure.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS0BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS0RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.debug.ui.commands.AddClassPrepareBreakpoint" commandName="Add Class Load Breakpoint" description="Add a class load breakpoint" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS0hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesCreateGroup" commandName="Create a Repository Group" description="Create a repository group for structuring repositories in the Git Repositories view" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS0xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.generate.javadoc" commandName="Generate JSDoc" description="Generates JSDoc for a selectable set of JavaScript resources" category="_XmI5-RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS1BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS1RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_XmI6ABTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS1hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tips.ide.command.open" commandName="Tip of the Day" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS1xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_XmI54xTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTS2BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_XmTS2RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS2hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS2xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_XmI58hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS3BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.method.exits" commandName="Search Method Exit Occurrences in File" description="Search for method exit occurrences of a selected return type" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS3RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS3hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS3xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_XmI58hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS4BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm4e.languageconfiguration.togglelinecommentcommand" commandName="Toggle Line Comment" category="_XmI54hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS4RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS4hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS4xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS5BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.m2e.sourcelookup.ui.importBinaryProject" commandName="Import Binary Project" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS5RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS5hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS5xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Field" description="Convert a local variable to a field" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS6BTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.ui.convertToJava" commandName="Convert to Java" description="Converts a file with a groovy extension to a file with a java extension" category="_XmI57hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS6RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS6hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS6xTCEe2ht7vZ3PkGHQ" elementId="de.loskutov.FSForceSyncCommand" commandName="Force File Synchronization" description="Manually triggers FileSync builder" category="_XmI57BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS7BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS7RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS7hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_XmI5-RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS7xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Field" description="Create getting and setting methods for the field and use only those to access the field" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS8BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS8RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS8hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.sse.ui.remove.block.comment" commandName="Remove Block Comment" description="Remove Block Comment" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS8xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Vars" description="Choose vars to initialize and constructor from superclass to call " category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS9BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS9RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.m2e.core.ui.command.openPom" commandName="Open Maven POM" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS9hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.index.rebuild" commandName="Rebuild Java Index" description="Rebuilds the Java index database" category="_XmI5-RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS9xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS-BTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.refactoring.command.rename" commandName="Rename - Refactoring" category="_XmI57hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS-RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xsd.ui.refactor.rename.element" commandName="&amp;Rename XSD element" description="Rename XSD element" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS-hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS-xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTS_BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_XmI54RTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTS_RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_XmTS_hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_XmTS_xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_XmTTABTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTARTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTAhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_XmI57RTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTTAxTCEe2ht7vZ3PkGHQ" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_XmTTBBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTBRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.selectRootElements" commandName="Select Root Elements" category="_XmI58RTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTTBhTCEe2ht7vZ3PkGHQ" elementId="type" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_XmTTBxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_XmI58hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTCBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTCRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTChTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTCxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTDBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.server.debug" commandName="Debug" description="Debug server" category="_XmI57xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTDRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTDhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new method parameter based on the selected expression" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTDxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.removeAllSessions" commandName="Remove All Sessions" category="_XmI58RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTEBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.run" commandName="Run Java Application" description="Run Java Application" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTERTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTEhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.sse.ui.structure.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTExTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.wsdl.ui.refactor.rename.element" commandName="Rename WSDL component" description="Renames WSDL component" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTFBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_XmI5-BTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTTFRTCEe2ht7vZ3PkGHQ" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_XmTTFhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTFxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTGBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTGRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTGhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTGxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_XmI5-RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTHBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTHRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTHhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTHxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.server.publish" commandName="Publish" description="Publish to server" category="_XmI57xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTIBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory method to encapsulate invocation of the selected constructor" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTIRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTIhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTIxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.sse.ui.structure.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTJBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTJRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_XmI58xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTJhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTJxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xsd.ui.refactor.makeElementGlobal" commandName="Make Local Element &amp;Global" description="Promotes local element to global level and replaces its references" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTKBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.encapsulateField.assist" commandName="Quick Assist - Create getter/setter for field" description="Invokes quick assist and selects 'Create getter/setter for field'" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTKRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.oomph.setup.editor.openEditorDropdown" commandName="Open Setup Editor" category="_XmI56BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTKhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_XmI5-BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTKxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTLBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.m2e.sourcelookup.ui.openPom" commandName="Open Pom" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTLRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.oomph.setup.ui.questionnaire" commandName="Configuration Questionnaire" description="Review the IDE&amp;apos;s most fiercely contested preferences" category="_XmI56BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTLhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTLxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTMBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_XmI5-BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTMRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTMhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.debug" commandName="Debug Java Application" description="Debug Java Application" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTMxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTNBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTNRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.buildship.ui.commands.openbuildscript" commandName="Open Gradle Build Script" description="Opens the Gradle build script for the selected Gradle project" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTNhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.FetchGitHubPR" commandName="Fetch GitHub Pull Request" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTNxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.buildship.ui.commands.openrunconfiguration" commandName="Open Gradle Run Configuration" description="Opens the Run Configuration for the selected Gradle tasks" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTOBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTORTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTOhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTOxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTPBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTPRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTPhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_XmI5-hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTPxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTQBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_XmI59BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTQRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xml.ui.generate.xml" commandName="XML File..." description="Generate a XML file from the selected DTD or Schema" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTQhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTQxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to var" description="Invokes quick assist and selects 'Assign parameter to var'" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTRBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_XmI6ARTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTTRRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_XmTTRhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTRxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_XmI58hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTSBTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.ui.convertToMethod" commandName="Convert to Method" description="Convert a closure declaration to a method" category="_XmI57hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTSRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTShTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTSxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTTBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTTRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTThTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTTxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static field and uses the new static field" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTUBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTURTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.sse.ui.cleanup.document" commandName="Cleanup Document..." description="Cleanup document" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTUhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTUxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTVBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTVRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTVhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify field access" description="Invokes quick assist and selects 'Qualify field access'" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTVxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTWBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.sse.ui.format" commandName="Format" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTWRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTWhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTWxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTXBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTXRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTXhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.m2e.core.ui.command.addPlugin" commandName="Add Maven Plugin" description="Add Maven plugin" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTXxTCEe2ht7vZ3PkGHQ" elementId="sed.tabletree.expandAll" commandName="Expand All" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTYBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.modify.method.parameters" commandName="Change Method Signature" description="Change method signature includes parameter names and parameter order" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTYRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTYhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.mergeSessions" commandName="Merge Sessions" category="_XmI58RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTYxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable (replace all occurrences)" description="Invokes quick assist and selects 'Extract local variable (replace all occurrences)'" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTZBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTZRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTZhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_XmI5-BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTZxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_XmI59BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTaBTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.groovyConsoleLaunchShortcut.run" commandName="Run Groovy Console" description="Run Groovy Console" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTaRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.external.javadoc" commandName="Open External JSDoc" description="Open the JSDoc of the selected element in an external browser" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTahTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTaxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTbBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTbRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_XmI6ABTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTTbhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_XmTTbxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_XmTTcBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTcRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTchTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTcxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.debug" commandName="Debug Java Applet" description="Debug Java Applet" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTdBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xml.ui.gotoMatchingTag" commandName="Matching Tag" description="Go to Matching Tag" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTdRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTdhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTdxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTeBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTeRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_XmI59BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTehTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_XmI5-RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTexTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.history.ShowBlame" commandName="Show Revision Information" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTfBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTfRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tips.ide.command.trim.open" commandName="Tip of the Day" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTfhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTfxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.selectCounters" commandName="Select Counters" category="_XmI58RTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTTgBTCEe2ht7vZ3PkGHQ" elementId="type" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_XmTTgRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_XmI5-RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTghTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTgxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.epp.package.common.contribute" commandName="Contribute" description="Contribute to the development and success of the Eclipse IDE!" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTThBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTThRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.extractLocalNotReplaceOccurrences.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTThhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTThxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_XmI6ARTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTiBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_XmI58hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTiRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.debug.ui.commands.AllReferences" commandName="All References" description="Inspect all references to the selected object" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTihTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.junit.junitShortcut.rerunLast" commandName="Rerun JUnit Test" description="Rerun JUnit Test" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTixTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTjBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.sse.ui.format.active.elements" commandName="Format Active Elements" description="Format active elements" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTjRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTjhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTjxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTkBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTkRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTkhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTkxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_XmI5-BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTlBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTlRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTlhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.m2e.actions.LifeCycleClean.run" commandName="Run Maven Clean" description="Run Maven Clean" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTlxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTmBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTmRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTmhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTmxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTnBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.debug.ui.commands.ForceReturn" commandName="Force Return" description="Forces return from method with value of selected expression " category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTnRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTnhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.importSession" commandName="Import Session..." category="_XmI58RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTnxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTToBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTToRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.lsp4e.toggleSortOutline" commandName="Sort" category="_XmI58BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTohTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_XmI5_RTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTToxTCEe2ht7vZ3PkGHQ" elementId="title" name="Title"/>
    <parameters xmi:id="_XmTTpBTCEe2ht7vZ3PkGHQ" elementId="message" name="Message"/>
    <parameters xmi:id="_XmTTpRTCEe2ht7vZ3PkGHQ" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_XmTTphTCEe2ht7vZ3PkGHQ" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_XmTTpxTCEe2ht7vZ3PkGHQ" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_XmTTqBTCEe2ht7vZ3PkGHQ" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_XmTTqRTCEe2ht7vZ3PkGHQ" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_XmTTqhTCEe2ht7vZ3PkGHQ" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_XmTTqxTCEe2ht7vZ3PkGHQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_XmTTrBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTrRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTrhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xml.ui.referencedFileErrors" commandName="Show Details..." description="Show Details..." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTrxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTsBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTsRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.debug.ui.command.OpenFromClipboard" commandName="Open from Clipboard" description="Opens a Java element or a Java stack trace from clipboard" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTshTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTsxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTtBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTtRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTthTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTtxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTuBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.JavaHierarchyPerspective" commandName="Java Type Hierarchy" description="Show the Java Type Hierarchy perspective" category="_XmI6ABTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTuRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTuhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.multicatch" commandName="Surround with try/multi-catch Block" description="Surround the selected text with a try/multi-catch block" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTuxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTvBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTvRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTvhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_XmI6BxTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTTvxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_XmTTwBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_XmTTwRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTwhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTwxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter.object" commandName="Introduce Parameter Object" description="Introduce a parameter object to a selected method" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTxBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.commands.openElementInEditor" commandName="Open Java Element" description="Open a Java element in its editor" category="_XmI57RTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTTxRTCEe2ht7vZ3PkGHQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_XmTTxhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.gef.zoom_out" commandName="Zoom Out" description="Zoom Out" category="_XmI6AxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTxxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected JavaScript comment lines" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTyBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTyRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTyhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.buildship.ui.commands.refreshtaskview" commandName="Refresh View (Gradle Tasks)" description="Refreshes the Gradle Tasks view" category="_XmI54RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTyxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.sse.ui.toggle.comment" commandName="Toggle Comment" description="Toggle Comment" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTzBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_XmI5-BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTzRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTzhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTTzxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT0BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.oomph.setup.editor.synchronizePreferences" commandName="Synchronize Preferences" category="_XmI56BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT0RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xml.ui.cmnd.contentmodel.sych" commandName="Synch" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT0hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT0xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT1BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT1RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xsd.ui.refactor.renameTargetNamespace" commandName="Rename Target Namespace" description="Changes the target namespace of the schema" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT1hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT1xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_XmI54BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT2BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.generate.javadoc" commandName="Generate Javadoc" description="Generates Javadoc for a selectable set of Java resources" category="_XmI5-RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT2RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT2hTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.dsl.command.uncheck_types" commandName="Remove type checking annotations" description="Removes type checking annotations from selected resources" category="_XmI55xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT2xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.debug.ui.commands.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT3BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.buildship.ui.shortcut.test.run" commandName="Run Gradle Test" description="Run Gradle test based on the current selection" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT3RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT3hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT3xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_XmI5-hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT4BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT4RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT4hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT4xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.tostring" commandName="Generate toString()" description="Generates the toString() method for the type" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT5BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT5RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT5hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT5xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.debug.ui.commands.Display" commandName="Display" description="Display result of evaluating selected text" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT6BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT6RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_XmI58xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT6hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT6xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to field" description="Invokes quick assist and selects 'Assign to field'" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT7BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT7RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.method" commandName="Extract Method" description="Extract a set of statements or an expression into a new method and use the new method" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT7hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT7xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.CompareWithEachOther" commandName="Compare with Each Other" description="Compare two files selected in the Compare Editor with each other." category="_XmI59BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT8BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jst.jsp.ui.refactor.rename" commandName="Rename" description="Rename a Java Element" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT8RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT8hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.navigate.gotopackage" commandName="Go to Package" description="Go to Package" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT8xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT9BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT9RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.buildship.ui.commands.addbuildshipnature" commandName="Add Gradle Nature" description="Adds the Gradle nature and synchronizes this project as if the Gradle Import wizard had been run on its location." category="_XmI55RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT9hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT9xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT-BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT-RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT-hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT-xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT_BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT_RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT_hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.server.run" commandName="Run" description="Run server" category="_XmI57xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTT_xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.testNgShortcut.coverage" commandName="Coverage TestNG Test" description="Coverage TestNG Test" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUABTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUARTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.groovyScriptLaunchShortcut.coverage" commandName="Coverage Groovy Script" description="Coverage Groovy Script" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUAhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_XmI59hTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTUAxTCEe2ht7vZ3PkGHQ" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_XmTUBBTCEe2ht7vZ3PkGHQ" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_XmTUBRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_XmI5-BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUBhTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.debug.ui.groovyShellLaunchShortcut.debug" commandName="Debug Groovy Shell" description="Debug Groovy Shell" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUBxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.common.project.facet.ui.ConvertProjectToFacetedForm" commandName="Convert to Faceted Form..." category="_XmI58hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUCBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.annotate.classFile" commandName="Annotate Class File" description="Externally add Annotations to a Class File." category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUCRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUChTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUCxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.sse.ui.format.document" commandName="Format" description="Format selection" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUDBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUDRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUDhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUDxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_XmI58hTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTUEBTCEe2ht7vZ3PkGHQ" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_XmTUERTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUEhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected Java comment lines" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUExTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUFBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui.selectActiveSession" commandName="Select Active Session..." category="_XmI58RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUFRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with Each Other" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUFhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUFxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUGBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUGRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_XmI5_xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUGhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_XmI5-xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUGxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUHBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_XmI6BxTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTUHRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_XmTUHhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_XmTUHxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.gotoBreadcrumb" commandName="Show In Breadcrumb" description="Shows the Java editor breadcrumb and sets the keyboard focus into it" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUIBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.userstorage.ui.showPullDown" commandName="Show Pull Down Menu" category="_XmI5-BTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTUIRTCEe2ht7vZ3PkGHQ" elementId="intoolbar" name="In Tool Bar" optional="false"/>
  </commands>
  <commands xmi:id="_XmTUIhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.class" commandName="Extract Class..." description="Extracts fields into a new class" category="_XmI59xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUIxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_XmI6BRTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUJBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_XmI59BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUJRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUJhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUJxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into JavaScript comments" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUKBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUKRTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.groovyScriptLaunchShortcut.run" commandName="Run Groovy Script" description="Run Groovy Script" category="_XmI5_BTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUKhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_XmI56RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUKxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.sse.ui.search.find.occurrences" commandName="Occurrences in File" description="Find occurrences of the selection in the file" category="_XmI54xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTULBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_XmI59hTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTULRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTULhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.sse.ui.outline.customFilter" commandName="&amp;Filters" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTULxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_XmI57RTCEe2ht7vZ3PkGHQ">
    <parameters xmi:id="_XmTUMBTCEe2ht7vZ3PkGHQ" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_XmTUMRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_XmI56xTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XmTUMhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the JavaScript file" category="_XmI57RTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_Xy3HQBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.ant.ui.actionSet.presentation/org.eclipse.ant.ui.toggleAutoReconcile" commandName="Toggle Ant Editor Auto Reconcile" description="Toggle Ant Editor Auto Reconcile" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_Xy3HQRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_Xy3HQhTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_Xy3HQxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_Xy3HRBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_Xy3HRRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_Xy3HRhTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_Xy3HRxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_Xy3HSBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_Xy3HSRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_Xy3HShTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.eclemma.ui.CoverageActionSet/org.eclipse.eclemma.ui.actions.CoverageDropDownAction" commandName="Coverage" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_Xy3HSxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.eclemma.ui.CoverageActionSet/org.eclipse.eclemma.ui.actions.CoverageAsAction" commandName="Coverage As" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_Xy3HTBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.eclemma.ui.CoverageActionSet/org.eclipse.eclemma.ui.actions.CoverageHistoryAction" commandName="Coverage History" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_Xy3HTRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4QBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New Java Class" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4QRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenPackageWizard" commandName="Package..." description="New Java Package" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4QhTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenProjectWizard" commandName="Java Project..." description="New Java Project" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4QxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.SearchActionSet/org.eclipse.jdt.ui.actions.OpenJavaSearchPage" commandName="Java..." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4RBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4RRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4RhTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4RxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4SBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4SRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.codehaus.groovy.eclipse.ui.groovyElementCreation/opengroovyprojectwizard" commandName="Groovy Project..." description="New Groovy Project" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4ShTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.new.actionSet/org.eclipse.wst.server.ui.action.new.server" commandName="Create Server" description="Create Server" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4SxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.open" commandName="Open Web Browser" description="Open Web Browser" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4TBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.switch" commandName="Web Browser" description="Web Browser" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4TRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.web.ui.wizardsActionSet/org.eclipse.wst.web.ui.actions.newCSSFile" commandName="CSS" description="Create a new Cascading Style Sheet" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4ThTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.web.ui.wizardsActionSet/org.eclipse.wst.web.ui.actions.newJSFile" commandName="JavaScript" description="Create a new JavaScript file" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4TxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.web.ui.wizardsActionSet/org.eclipse.wst.web.ui.actions.newHTMLFile" commandName="HTML" description="Create a new HTML page" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4UBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jst.j2ee.J2eeMainActionSet/org.eclipse.jst.j2ee.internal.actions.NewJavaEEArtifact" commandName="Servlet" description="Create a new Servlet" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4URTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jst.j2ee.J2eeMainActionSet/org.eclipse.jst.j2ee.internal.actions.NewJavaEEProject" commandName="Dynamic Web Project" description="Create a Dynamic Web project" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4UhTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.ws.explorer.explorer/org.eclipse.wst.ws.internal.explorer.action.LaunchWSEAction" commandName="Launch the Web Services Explorer" description="Launch the Web Services Explorer" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4UxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.ant.ui.BreakpointRulerActions/org.eclipse.ant.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4VBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4VRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4VhTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4VxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4WBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetExecute" commandName="Execute" description="Execute the Selected Text" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4WRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetDisplay" commandName="Display" description="Display Result of Evaluating Selected Text" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4WhTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetInspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4WxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4XBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4XRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4XhTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4XxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4YBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution/org.eclipse.m2e.jdt.ui.downloadSourcesAction" commandName="label" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4YRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution_38/org.eclipse.m2e.jdt.ui.downloadSourcesAction_38" commandName="label" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4YhTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Text Editor Bookmark Ruler Action" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4YxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4ZBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.codehaus.groovy.editor.actions/org.codehaus.groovy.editor.ruler.ToggleBreakpointRulerAction" commandName="Toggle Breakpoint" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4ZRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.codehaus.groovy.editor.actions/org.codehaus.groovy.editor.ruler.SelectAnnotationRulerAction" commandName="Select Annotation" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4ZhTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.codehaus.groovy.class_editor.actions/org.codehaus.groovy.class_editor.ruler.ToggleBreakpointRulerAction" commandName="Toggle Breakpoint" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4ZxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.textEditor.rulerActions/org.eclipse.lsp4e.debug.textEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4aBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.genericEditor.rulerActions/org.eclipse.lsp4e.debug.genericEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4aRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.css.core.csssource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4ahTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.css.core.csssource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4axTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.dtd.core.dtdsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4bBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.dtd.core.dtdsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4bRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.html.core.htmlsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4bhTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.html.core.htmlsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4bxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="JavaScript Editor Bookmark Ruler Action" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4cBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="JavaScript Editor Ruler Single-Click" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4cRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="JavaScript Editor Ruler Single-Click" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4chTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="JavaScript Editor Bookmark Ruler Action" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4cxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="JavaScript Editor Ruler Single-Click" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4dBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.json.core.jsonsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="%AddBookmark.label" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4dRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.json.core.jsonsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="%SelectRuler.label" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4dhTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4dxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4eBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.xsd.core.xsdsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4eRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.xsd.core.xsdsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4ehTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jst.jsp.core.jspsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4exTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jst.jsp.core.jspsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4fBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.ui.articles.action.contribution.editor/org.eclipse.wst.wsdl.ui.actions.ReloadDependenciesActionDelegate" commandName="Reload Dependencies" description="Reload Dependencies" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4fRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.wsdl.wsdlsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4fhTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.wst.wsdl.wsdlsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4fxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4gBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4gRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4ghTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4gxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4hBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzA4hRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCMBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCMRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCMhTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCMxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCNBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCNRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCNhTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCNxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCOBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCORTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCOhTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCOxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCPBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCPRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCPhTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variablesViewActions.AllReferencesInView" commandName="Show References" description="Shows references to each object in the variables view as an array of objects." category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCPxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCQBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCQRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCQhTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCQxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCRBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.AllReferencesInView" commandName="Show References" description="Show &amp;References" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCRRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCRhTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCRxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCSBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCSRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.actions.AddException" commandName="Add Java Exception Breakpoint" description="Add Java Exception Breakpoint" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCShTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCSxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowThreadGroups" commandName="Show Thread Groups" description="Show Thread Groups" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCTBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCTRTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowSystemThreads" commandName="Show System Threads" description="Show System Threads" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCThTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowRunningThreads" commandName="Show Running Threads" description="Show Running Threads" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCTxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowMonitorThreadInfo" commandName="Show Monitors" description="Show the Thread &amp; Monitor Information" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCUBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Watch" commandName="Watch" description="Create a Watch Expression from the Selected Text" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCURTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Execute" commandName="Execute" description="Execute the Selected Text" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCUhTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Display" commandName="Display" description="Display Result of Evaluating Selected Text" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCUxTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Inspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <commands xmi:id="_XzKCVBTCEe2ht7vZ3PkGHQ" elementId="AUTOGEN:::org.eclipse.ui.articles.action.contribution.view/org.eclipse.wst.wsi.ui.internal.actions.actionDelegates.ValidateWSIProfileActionDelegate" commandName="WS-I Profile Validator" description="Validate WS-I Message Log File" category="_XmI6BxTCEe2ht7vZ3PkGHQ"/>
  <addons xmi:id="_XlnVfhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_XlnVfxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_XlnVgBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_XlnVgRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_XlnVghTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_XlnVgxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_XlnVhBTCEe2ht7vZ3PkGHQ" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_XlnVhRTCEe2ht7vZ3PkGHQ" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_XlnVhhTCEe2ht7vZ3PkGHQ" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_XlnVhxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_XlwfYBTCEe2ht7vZ3PkGHQ" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_loBDwBTDEe2CPJjvlHg64Q" elementId="org.eclipse.ui.ide.addon.0" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_dz0JgGOlEeSMMaPQU2nlzw" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_XmI54BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_XmI54RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_XmI54hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm4e.languageconfiguration.category" name="TM4E Language Configuration"/>
  <categories xmi:id="_XmI54xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_XmI55BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_XmI55RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.buildship.ui.project" name="Buildship" description="Contains the Buildship specific commands"/>
  <categories xmi:id="_XmI55hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_XmI55xTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.dsl.command.category" name="DSLD-related commands" description="Commands related to DSLD management"/>
  <categories xmi:id="_XmI56BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.oomph.setup.category" name="Oomph Setup"/>
  <categories xmi:id="_XmI56RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_XmI56hTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.category.compiler" name="Groovy Compiler Commands"/>
  <categories xmi:id="_XmI56xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.jsdt.ui.category.source" name="Source" description="JavaScript Source Actions"/>
  <categories xmi:id="_XmI57BTCEe2ht7vZ3PkGHQ" elementId="de.loskutov.FileSync" name="FileSync" description="File Synchronisation"/>
  <categories xmi:id="_XmI57RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_XmI57hTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.refactoring.commands.refactoring" name="Groovy Refactoring Commands" description="Refactorings"/>
  <categories xmi:id="_XmI57xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.server.ui" name="Server" description="Server"/>
  <categories xmi:id="_XmI58BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.lsp4e.category" name="Language Servers"/>
  <categories xmi:id="_XmI58RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.eclemma.ui" name="EclEmma Code Coverage"/>
  <categories xmi:id="_XmI58hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_XmI58xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_XmI59BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_XmI59RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_XmI59hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_XmI59xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.category.refactoring" name="Refactor - Java" description="Java Refactoring Actions"/>
  <categories xmi:id="_XmI5-BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_XmI5-RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_XmI5-hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_XmI5-xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_XmI5_BTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_XmI5_RTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_XmI5_hTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.oomph" name="Oomph"/>
  <categories xmi:id="_XmI5_xTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_XmI6ABTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_XmI6ARTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_XmI6AhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.lsp4e.commandCategory" name="Command"/>
  <categories xmi:id="_XmI6AxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.gef.category.view" name="View" description="View"/>
  <categories xmi:id="_XmI6BBTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.wst.xml.views.XPathView" name="XPath"/>
  <categories xmi:id="_XmI6BRTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.jdt.ui.category.source" name="Source" description="Java Source Actions"/>
  <categories xmi:id="_XmI6BhTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.oomph.commands" name="Oomph"/>
  <categories xmi:id="_XmI6BxTCEe2ht7vZ3PkGHQ" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_XmI6CBTCEe2ht7vZ3PkGHQ" elementId="org.codehaus.groovy.eclipse.editor.category.source" name="Groovy Source Commands" description="Source"/>
</application:Application>
