<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    
    <modelVersion>4.0.0</modelVersion>
    
    <!-- Informações do Projeto -->
    <groupId>com.infoaxis</groupId>
    <artifactId>windchill-erp-integration</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>
    
    <name>Windchill ERP Integration Library</name>
    <description>Biblioteca para integração do Windchill com sistemas ERP (Datasul, Senior, Protheus)</description>
    
    <!-- Propriedades -->
    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        
        <!-- <PERSON><PERSON><PERSON><PERSON> das dependências -->
        <junit.version>5.10.0</junit.version>
        <mockito.version>5.5.0</mockito.version>
        <gson.version>2.10.1</gson.version>
        <logback.version>1.4.11</logback.version>
        
        <!-- Caminhos do Windchill -->
        <windchill.home>D:/Windchill</windchill.home>
        <windchill.codebase>${windchill.home}/codebase</windchill.codebase>
        <windchill.lib>${windchill.home}/lib</windchill.lib>
        <windchill.webinf.lib>${windchill.codebase}/WEB-INF/lib</windchill.webinf.lib>
    </properties>
    
    <!-- Dependências -->
    <dependencies>
        
        <!-- Dependências do Windchill Core -->
        <dependency>
            <groupId>com.ptc.windchill</groupId>
            <artifactId>windchill-core</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${windchill.codebase}</systemPath>
        </dependency>
        
        <!-- Bibliotecas principais do Windchill -->
        <dependency>
            <groupId>com.ptc.windchill</groupId>
            <artifactId>esi</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${windchill.lib}/esi.jar</systemPath>
        </dependency>
        
        <dependency>
            <groupId>com.ptc.windchill</groupId>
            <artifactId>servlet</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${windchill.lib}/servlet.jar</systemPath>
        </dependency>
        
        <dependency>
            <groupId>com.ptc.windchill</groupId>
            <artifactId>wnc</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${windchill.lib}/wnc.jar</systemPath>
        </dependency>
        
        <!-- Bibliotecas Web do Windchill -->
        <dependency>
            <groupId>com.ptc.windchill</groupId>
            <artifactId>ie-web</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${windchill.webinf.lib}/ieWeb.jar</systemPath>
        </dependency>
        
        <!-- Dependências externas -->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>${gson.version}</version>
        </dependency>
        
        <!-- Logging -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback.version}</version>
        </dependency>
        
        <!-- Dependências de Teste -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        
    </dependencies>
    
    <!-- Build Configuration -->
    <build>
        <sourceDirectory>src/main/java</sourceDirectory>
        <testSourceDirectory>src/test/java</testSourceDirectory>
        
        <!-- Diretório de saída personalizado para Windchill -->
        <outputDirectory>${windchill.codebase}</outputDirectory>
        
        <plugins>
            
            <!-- Compiler Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            
            <!-- Surefire Plugin para testes -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.1.2</version>
                <configuration>
                    <includes>
                        <include>**/*Test.java</include>
                        <include>**/*Tests.java</include>
                    </includes>
                </configuration>
            </plugin>
            
            <!-- Resources Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.1</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            
            <!-- Clean Plugin customizado -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <version>3.3.1</version>
                <configuration>
                    <filesets>
                        <fileset>
                            <directory>${windchill.codebase}/com/infoaxis</directory>
                            <includes>
                                <include>**/*.class</include>
                            </includes>
                        </fileset>
                    </filesets>
                </configuration>
            </plugin>
            
        </plugins>
    </build>
    
    <!-- Profiles para diferentes ambientes -->
    <profiles>
        
        <!-- Profile de desenvolvimento -->
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <maven.test.skip>false</maven.test.skip>
            </properties>
        </profile>
        
        <!-- Profile de produção -->
        <profile>
            <id>prod</id>
            <properties>
                <maven.test.skip>true</maven.test.skip>
            </properties>
        </profile>
        
    </profiles>
    
</project>
