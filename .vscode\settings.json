{"java.configuration.updateBuildConfiguration": "automatic", "java.project.sourcePaths": [".", "D:/Windchill/src"], "java.project.outputPath": "D:/Windchill/codebase", "java.project.referencedLibraries": {"include": ["D:/Windchill/lib/*.jar", "D:/Windchill/codebase/WEB-INF/lib/*.jar", "D:/Windchill/srclib/**/*.jar", "D:/Windchill/thirdparty/**/*.jar"], "exclude": ["**/test/**"], "sources": {"D:/Windchill/lib/*.jar": "D:/Windchill/src/**", "D:/Windchill/codebase/WEB-INF/lib/*.jar": "D:/Windchill/src/**"}}, "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx4G -Xms512m -Xlog:disable", "java.dependency.packagePresentation": "hierarchical", "java.debug.settings.onBuildFailureProceed": true, "java.compile.nullAnalysis.mode": "disabled", "java.dependency.syncWithFolderExplorer": false, "java.debug.settings.showQualifiedNames": false, "java.import.maven.enabled": false, "java.import.gradle.enabled": false, "java.project.encoding": "UTF-8", "java.completion.enabled": true, "java.completion.overwrite": false, "java.completion.guessMethodArguments": true, "java.signatureHelp.enabled": true, "java.contentProvider.preferred": "fernflower", "java.sources.organizeImports.starThreshold": 99, "java.sources.organizeImports.staticStarThreshold": 99, "java.eclipse.downloadSources": true, "java.maven.downloadSources": true, "java.project.resourceFilters": [{"node": "node_modules", "type": "excludeFolder"}, {"node": ".git", "type": "excludeFolder"}]}