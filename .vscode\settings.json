{"java.project.sourcePaths": ["D:/Windchill/src/com/infoaxis", "D:/Eclipse/cust_windchill_src"], "java.project.referencedLibraries": ["D:/Windchill/srclib/**/*.jar", "D:/Windchill/thirdparty/**/*.jar", "D:/Windchill/codebase/WEB-INF/lib/**/*.jar"], "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable", "java.dependency.packagePresentation": "hierarchical", "java.debug.settings.onBuildFailureProceed": true, "java.compile.nullAnalysis.mode": "disabled", "java.dependency.syncWithFolderExplorer": false, "java.debug.settings.showQualifiedNames": false}