eclipse.preferences.version=1
org.eclipse.jdt.core.builder.resourceCopyExclusionFilter=*.*~,*.bak,*.cat,*.checkedout,*.classpath,*.contrib*,*.copyarea.db,*.cup,*.flex,*.form,*.hijacked,*.java,*.jj,*.keep*,*.log,*.mData,*.mak,*.mdl,*.merge,*.mjava,*.mkelem,*.nbattrs,*.nbintdb,*.notes,*.notes.txt,*.project,*.rbInfo,*.unloaded/,*.vbp,*.vcproj*,*.ve2,*.vep,*.vpj,*.vssscc,*.wsp,Copy of *,Makefile*,Package.doc,build*.xml,doc-files/,lost+found/,makefile*,makefile.sub,overview.html,package.doc,package.html,relsrc.inc,*.class.path,*.ptcdarinfo,*.ptctarinfo,bin/,cb_registry/,conf/,db/,dca/,*.xconf,index.bpi,loadFiles/,lost+found,opt/,tasks/,testLoadFiles/,utilities/
org.eclipse.jdt.core.compiler.annotation.missingNonNullByDefaultAnnotation=ignore
org.eclipse.jdt.core.compiler.annotation.nonnull=org.eclipse.jdt.annotation.NonNull
org.eclipse.jdt.core.compiler.annotation.nonnullbydefault=org.eclipse.jdt.annotation.NonNullByDefault
org.eclipse.jdt.core.compiler.annotation.nullable=org.eclipse.jdt.annotation.Nullable
org.eclipse.jdt.core.compiler.annotation.nullanalysis=disabled
org.eclipse.jdt.core.compiler.problem.nullAnnotationInferenceConflict=error
org.eclipse.jdt.core.compiler.problem.nullReference=warning
org.eclipse.jdt.core.compiler.problem.nullSpecViolation=error
org.eclipse.jdt.core.compiler.problem.nullUncheckedConversion=warning
org.eclipse.jdt.core.compiler.problem.overridingPackageDefaultMethod=ignore
org.eclipse.jdt.core.compiler.problem.potentialNullReference=ignore
org.eclipse.jdt.core.compiler.problem.rawTypeReference=ignore
org.eclipse.jdt.core.compiler.problem.syntacticNullAnalysisForFields=disabled
org.eclipse.jdt.core.compiler.problem.unhandledWarningToken=ignore
org.eclipse.jdt.core.compiler.problem.unusedWarningToken=ignore
org.eclipse.jdt.core.compiler.processAnnotations=enabled
